{"version": "1.1.1-2", "name": "gdlog-client-app", "license": "MIT", "scripts": {"analyze:clean": "rimraf stats.json", "preanalyze": "npm run analyze:clean", "analyze": "node ./internals/scripts/analyze.js", "extract-intl": "babel-node --presets env,stage-0 -- ./internals/scripts/extract-intl.js", "npmcheckversion": "node ./internals/scripts/npmcheckversion.js", "preinstall": "npm run npmcheckversion", "postinstall": "npm run build:dll", "prebuild": "npm run build:clean", "version": "node ./internals/scripts/version.js", "build": "cross-env NODE_ENV=production webpack --config internals/webpack/webpack.prod.babel.js --color -p --progress --hide-modules --display-optimization-bailout", "build:clean": "rimraf ./build", "build:dll": "node ./internals/scripts/dependencies.js", "start": "cross-env NODE_ENV=development node server", "start:tunnel": "cross-env NODE_ENV=development ENABLE_TUNNEL=true node server", "start:production": "npm run test && npm run build && npm run start:prod", "start:prod": "cross-env NODE_ENV=production node server", "presetup": "npm i chalk shelljs", "setup": "node ./internals/scripts/setup.js", "postsetup": "npm run build:dll", "clean": "shjs ./internals/scripts/clean.js", "clean:all": "npm run analyze:clean && npm run test:clean && npm run build:clean", "generate": "plop --plopfile internals/generators/index.js", "lint": "npm run lint:js", "lint:eslint": "eslint --ignore-path .gitignore --ignore-pattern internals/scripts", "lint:js": "npm run lint:eslint -- . ", "lint:staged": "lint-staged", "pretest": "npm run test:clean", "test:clean": "rimraf ./coverage", "test": "cross-env NODE_ENV=test jest --coverage", "test:watch": "cross-env NODE_ENV=test jest --watchAll", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "babel": {"plugins": ["styled-components", ["babel-plugin-transform-builtin-extend", {"globals": ["Error", "Array"]}]], "presets": [["env", {"modules": false}], "react", "stage-0"], "env": {"production": {"only": ["app"], "plugins": ["transform-react-remove-prop-types", "transform-react-constant-elements", "transform-react-inline-elements"]}, "test": {"plugins": ["transform-es2015-modules-commonjs", "dynamic-import-node"]}}}, "eslintConfig": {"parser": "babel-es<PERSON>", "extends": "airbnb", "env": {"browser": true, "node": true, "jest": true, "es6": true}, "plugins": ["redux-saga", "react", "jsx-a11y"], "parserOptions": {"ecmaVersion": 8, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "rules": {"arrow-parens": ["error", "always"], "arrow-body-style": [2, "as-needed"], "class-methods-use-this": 0, "comma-dangle": [2, "always-multiline"], "import/imports-first": 0, "import/newline-after-import": 0, "import/no-dynamic-require": 0, "import/no-extraneous-dependencies": 0, "import/no-named-as-default": 0, "import/no-unresolved": 2, "import/no-webpack-loader-syntax": 0, "import/prefer-default-export": 0, "indent": [2, 2, {"SwitchCase": 1}], "jsx-a11y/aria-props": 2, "jsx-a11y/heading-has-content": 0, "jsx-a11y/href-no-hash": 2, "jsx-a11y/label-has-for": 2, "jsx-a11y/mouse-events-have-key-events": 2, "jsx-a11y/role-has-required-aria-props": 2, "jsx-a11y/role-supports-aria-props": 2, "max-len": 0, "newline-per-chained-call": 0, "no-confusing-arrow": 0, "no-console": 0, "no-use-before-define": 0, "prefer-template": 2, "react/forbid-prop-types": 0, "react/jsx-first-prop-new-line": [2, "multiline"], "react/jsx-filename-extension": 0, "react/jsx-no-target-blank": 0, "react/require-default-props": 0, "react/require-extension": 0, "react/self-closing-comp": 0, "redux-saga/no-yield-in-race": 2, "redux-saga/yield-effects": 2, "require-yield": 0, "react/no-unescaped-entities": 0, "no-useless-escape": 0, "react/prop-types": 0, "react/sort-comp": 0, "import/first": 0, "react/no-array-index-key": 0, "no-param-reassign": 0, "react/no-unused-prop-types": 1}, "settings": {"import/resolver": {"webpack": {"config": "./internals/webpack/webpack.prod.babel.js"}}}}, "dllPlugin": {"path": "node_modules/react-boilerplate-dlls", "exclude": ["chalk", "compression", "cross-env", "express", "ip", "minimist", "sanitize.css"], "include": ["core-js", "lodash", "eventsource-polyfill"]}, "jest": {"collectCoverageFrom": ["app/**/*.{js,jsx}", "!app/**/*.test.{js,jsx}", "!app/*/RbGenerated*/*.{js,jsx}", "!app/app.js", "!app/global-styles.js", "!app/*/*/Loadable.{js,jsx}"], "coverageThreshold": {"global": {"statements": 98, "branches": 91, "functions": 98, "lines": 98}}, "moduleDirectories": ["node_modules", "app"], "moduleNameMapper": {".*\\.(css|less|styl|scss|sass)$": "<rootDir>/internals/mocks/cssModule.js", ".*\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/internals/mocks/image.js"}, "setupTestFrameworkScriptFile": "<rootDir>/internals/testing/test-bundler.js", "testRegex": "tests/.*\\.test\\.js$", "setupFiles": ["jest-localstorage-mock"]}, "dependencies": {"@blueprintjs/core": "^3.12.0", "@blueprintjs/datetime": "^3.7.0", "@blueprintjs/icons": "^3.5.1", "@blueprintjs/select": "^3.6.0", "@blueprintjs/table": "^3.4.1", "@sentry/browser": "^5.5.0", "@tinymce/tinymce-react": "^3.3.2", "await-to-js": "^2.1.1", "axios": "^0.19.0", "babel-plugin-transform-builtin-extend": "^1.1.2", "babel-polyfill": "6.23.0", "bootstrap": "^4.2.1", "calidation": "^1.7.0", "chalk": "^1.1.3", "ckeditor4-react": "^1.4.2", "classnames": "2.2.6", "compression": "^1.7.3", "cross-env": "5.0.0", "express": "^4.16.4", "file-saver": "^2.0.5", "fontfaceobserver": "2.0.9", "formik": "1.5.1", "g": "^2.0.1", "history": "4.6.3", "hoist-non-react-statics": "2.1.1", "immutable": "3.8.1", "intl": "1.2.5", "invariant": "2.2.2", "ip": "1.1.5", "jest-localstorage-mock": "^2.4.0", "jquery": "^3.3.1", "jwt-decode": "^2.2.0", "latinize": "^0.4.0", "lodash": "^4.17.11", "loglevel": "^1.6.1", "minimist": "1.2.0", "moment": "2.24.0", "namor": "1.1.1", "numeral": "2.0.6", "perfect-scrollbar": "1.4.0", "polished": "^3.0.0", "prop-types": "15.5.10", "query-string": "5.1.1", "react": "16.3.2", "react-addons-css-transition-group": "^15.6.2", "react-bootstrap": "^1.0.0-beta.5", "react-copy-to-clipboard": "^5.1.0", "react-custom-scrollbars": "^4.2.1", "react-day-picker": "7.3.0", "react-dom": "16.4.2", "react-dropzone": "^7.0.1", "react-ga": "^2.7.0", "react-geocode": "0.1.2", "react-google-maps": "^9.4.5", "react-helmet": "^5.1.3", "react-image-gallery": "^0.8.14", "react-intl": "2.8.0", "react-js-pagination": "^3.0.2", "react-json-view": "^1.21.3", "react-lines-ellipsis": "^0.14.0", "react-loadable": "4.0.3", "react-mobile-store-button": "0.0.4", "react-modal-image": "^2.3.2", "react-number-format": "^4.0.7", "react-paginate": "^6.3.0", "react-places-autocomplete": "7.2.0", "react-pose": "^4.0.8", "react-qrcode-logo": "^2.8.0", "react-rangeslider": "^2.2.0", "react-rating": "^1.6.2", "react-redux": "5.0.5", "react-router": "4.3.1", "react-router-dom": "4.3.1", "react-router-redux": "5.0.0-alpha.9", "react-scrollbars-custom": "^4.0.0-alpha.10", "react-select": "^2.4.1", "react-share": "2.4.0", "react-shimmer": "^2.0.0", "react-table": "6.8.6", "react-tooltip": "^3.10.0", "react-truncate": "2.4.0", "react-virtualized": "^9.21.0", "reactstrap": "^7.1.0", "redux": "3.6.0", "redux-immutable": "4.0.0", "redux-saga": "0.15.3", "reselect": "3.0.1", "sinon": "^7.2.4", "styled-components": "3.4.10", "warning": "3.0.0", "whatwg-fetch": "2.0.3", "yup": "0.27.0"}, "devDependencies": {"add-asset-html-webpack-plugin": "2.0.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-eslint": "7.2.3", "babel-loader": "7.1.0", "babel-plugin-dynamic-import-node": "1.0.2", "babel-plugin-react-intl": "2.3.1", "babel-plugin-react-transform": "2.0.2", "babel-plugin-styled-components": "1.1.4", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-plugin-transform-react-constant-elements": "6.23.0", "babel-plugin-transform-react-inline-elements": "6.22.0", "babel-plugin-transform-react-remove-prop-types": "0.4.5", "babel-preset-env": "^1.7.0", "babel-preset-react": "6.24.1", "babel-preset-stage-0": "6.24.1", "chance": "^1.0.18", "circular-dependency-plugin": "3.0.0", "coveralls": "^3.0.3", "css-loader": "^2.1.1", "compression-webpack-plugin": "1.1.7", "empty-module": "0.0.2", "enzyme": "3.9.0", "eslint": "3.19.0", "eslint-config-airbnb": "15.0.1", "eslint-config-airbnb-base": "11.2.0", "eslint-import-resolver-webpack": "0.8.3", "eslint-plugin-import": "2.7.0", "eslint-plugin-jsx-a11y": "5.0.3", "eslint-plugin-react": "7.0.1", "eslint-plugin-redux-saga": "0.3.0", "eventsource-polyfill": "0.9.6", "exports-loader": "0.6.4", "file-loader": "0.11.1", "html-loader": "0.4.5", "html-webpack-plugin": "2.29.0", "image-webpack-loader": "^5.0.0", "imports-loader": "0.7.1", "jest-cli": "^24.8.0", "lint-staged": "3.5.1", "node-plop": "0.7.0", "node-sass": "^6.0.0", "null-loader": "0.1.1", "offline-plugin": "^5.0.7", "plop": "1.8.0", "react-test-renderer": "16.8.3", "rimraf": "2.6.1", "shelljs": "^0.7.7", "style-loader": "0.18.1", "url-loader": "^1.1.2", "webpack": "3.5.5", "webpack-dev-middleware": "1.11.0", "webpack-hot-middleware": "2.18.0"}}