import styled, {css} from 'styled-components';
import breakpoint from 'styles/breakpoint';

export default styled.div`
  //background-color: #f4f4f4;
  display: flex;
  flex-direction: column;
  min-height: ${(props) => props.isListItem ? '120px;' : 'calc(100% - 1em)'};

  .post {
    &__img {
      text-align: center;
      height: ${(props) => props.heightImg}px;
      padding-left: 0 !important;
      img, .bp3-spinner {
        height: 100%;
        width: auto;
        max-height: 100%;
        max-width: 100%;
      }
    }
    
    &__detail {
      padding: ${(props) => props.isListItem ? '10px' : '20px 0'};
      display: flex;
      flex-direction: column;
    }
    
    &__content{
      color: #4b545c;
      font-size: 14px;
      opacity: 0.8;
      padding-top: 15px;
      margin-bottom: 0 !important;
    }
    
    &__publishedDate {
      display: flex;
      flex-direction: row;
      align-content: center;
      color: #4b545c;
      font-size: 14px;
      opacity: 0.5;
      font-weight: 500;
      padding-top: 10px;
      img{
        padding-right: 10px;
      }
    }
    
    &__title {
      color: ${(props) => props.isListItem ? '#000000' : '#000000'};
      font-size: ${(props) => props.isListItem ? '16px' : '22px'};
      font-weight: ${(props) => props.isListItem ? '700' : '700'};
      //margin-bottom: ${(props) => props.isListItem ? '0' : '18px'};
      line-height: 1.2;
      .LinesEllipsis{
        opacity: .8;
      }
    }
    
    &__author {
      display: flex;
      align-items: center;
      margin-bottom: 50px;
    
      &__avatar {
        border-radius: 50%;
        overflow: hidden;
        border: solid 2px #f5a623;
        margin-right: 1em;
        padding: 3px;
        line-height: 1;
        flex: none;
        
        img {
          border-radius: 50%;
          overflow: hidden;
        }
      }
      
      &__name {
        font-size: ${(props) => props.theme.fontSizes.small};
        color: #2e3c52;
        line-height: 1.2;
      }
      
      &__username {
        font-size: ${(props) => props.theme.fontSizes.small};
        color: #24abb2;
        line-height: 1.2;
      }
    }
    
    &__category {
      font-size: 14px;
      font-weight: normal;
      margin-top: auto;
      color: #4b545c;
      text-transform: uppercase;
    }
    .hr-post-item{
        margin-top: 15px;
        margin-bottom: 0 !important;
    }
  }
  @media (max-width:${breakpoint.md}){
    .post {
      &__img {
        height:initial;
        img {
          height: auto;
          width: 100%;
        }
      }
    }
  }
  @media (max-width:${breakpoint.sm}){
    .post {
      &__detail{
        padding:10px;
      }
    }
  }
`;
