/**
 *
 * PostItem
 *
 */

import React from 'react';
// import styled from 'styled-components';
import PropTypes from 'prop-types';

import { FormattedMessage } from 'react-intl';
import messages from './messages';
import Wrapper from './styled/Wrapper';
import { Link } from 'react-router-dom';
import defaultImg from 'images/default-bank-logo.png';
import { formatDate } from 'utils/dateHelper';
import noAvatar from 'images/default-user-profile.png';
import Image from 'react-shimmer';
import { Spinner } from '@blueprintjs/core';
import LinesEllipsis from 'react-lines-ellipsis';
import HTMLEllipsis from 'react-lines-ellipsis/lib/html'
import icClock from 'images/icons/ic_clock.svg';

class PostItem extends React.Component {
  getPostContent = (props) => {
    if (props.post) {
      return { __html: props.post.excerpt.rendered.substring(0, props.post.excerpt.rendered.length - 16).concat('.') };
    }
    return { __html: props.post.excerpt.rendered.substring(0, props.post.excerpt.rendered.length - 16).concat('.') };
  }

  render() {
    const { post, isListItem, className, heightImg, isPostItem } = this.props;
    const { _embedded, slug, title, modified } = post || {};
    const category = _embedded['wp:term'] ? _embedded['wp:term'][0][0] : null;
    const featureMedia = _embedded['wp:featuredmedia'] ? _embedded['wp:featuredmedia'][0] : null;
    // const author = _embedded.author ? _embedded.author[0] : null;

    return (
      <Link to={`/tin-tuc/${category ? category.slug : ''}/${slug || ''}`}>
        <Wrapper isListItem={isListItem} className={className} heightImg={heightImg}>
          <div className={`${className} ${isPostItem ? 'd-flex flex-row' : ''}`}>
            <div className={`post__img ${isListItem ? 'col-md-4' : `${isPostItem ? 'col-md-5' : 'col-md-12 pr-0'}`}`}>
              <Image
                src={featureMedia ? featureMedia.source_url : defaultImg}
                alt={title ? title.rendered : ''}
                fallback={<Spinner size={Spinner.SIZE_STANDARD} />}
              />
            </div>
            <div className={`post__detail ${isPostItem ? 'col-md-7' : 'col-md-12 pl-0'}`}>
              <div className="post__title" >
                <HTMLEllipsis
                  unsafeHTML={title ? title.rendered : ''}
                  maxLine={isPostItem ? '2' : '3'}
                  ellipsis="..."
                  trimRight
                  basedOn="words"
                />
              </div>

              {<div className="post__publishedDate">
                <img src={icClock} alt={''} />
                {formatDate(modified)}
              </div>}

              {!isListItem && <div className="post__content" dangerouslySetInnerHTML={this.getPostContent(this.props)}>
              </div>}
            </div>
          </div>
          {isListItem && <div className={'px-0 mx-0'}>
            <hr className={'hr-post-item'} />
          </div>}
        </Wrapper>
      </Link>
    );
  }
}

PostItem.propTypes = {
  post: PropTypes.object.isRequired,
  isListItem: PropTypes.bool,
  className: PropTypes.string,
  heightImg: PropTypes.number,
  isPostItem: PropTypes.bool,
};

PostItem.defaultProps = {
  heightImg: 200,
};

export default PostItem;
