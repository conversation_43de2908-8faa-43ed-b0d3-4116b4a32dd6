/*
 * ButtonLink Messages
 *
 * This contains all the text for the ButtonLink component.
 */
import { defineMessages } from 'react-intl';

const scope = 'app.components.ActionGroupCell';

export default defineMessages({
  action: {
    id: `${scope}.action`,
    defaultMessage: '<PERSON><PERSON> tác',
  },
  actionLock: {
    id: `${scope}.actionLock`,
    defaultMessage: 'Tạm khóa',
  },
  actionEdit: {
    id: `${scope}.actionEdit`,
    defaultMessage: 'Cập nhật',
  },
  actionUnlock: {
    id: `${scope}.actionUnlock`,
    defaultMessage: 'Mở khóa',
  },
  actionView: {
    id: `${scope}.actionView`,
    defaultMessage: 'Xem chi tiết',
  },
  actionDelete: {
    id: `${scope}.actionDelete`,
    defaultMessage: 'Xóa',
  },
});
