import React from 'react';
import styled from 'styled-components';
import * as PropTypes from 'prop-types';
import { Classes, Icon, Popover, Position } from '@blueprintjs/core';

const StyledContainer = styled.div`
  
`;

const StyledContent = styled.div`
  a {
    &:nth-child(even) {
      .item {  
        background-color: #f5f5f5;
      }
    }
    
    &:nth-child(odd) {
      .item {  
        background-color: white;
      }
    }
  }
 
  .item {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 10px 20px 10px 10px;
    transition: 0.15s;
    
    img {    
      width: 14px;
      height: 14px;
      object-fit: contain;
      margin-right: 10px;
    }
    
    span {
      opacity: 0.8;
      font-size: 14px;
      color: #000000;
    }
    
    &:hover {
      background-color: rgba(167, 182, 194, 0.3) !important;
    }
  }
`;

const StyledTarget = styled.div` 
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.15s;

  svg {
    color: rgba(0, 0, 0, 0.1);
    transition: 0.15s;
  }

  &.disabled {
    cursor: not-allowed;
  }

  &:not(.disabled) {
    &:hover, &.bp3-active {
      cursor: pointer;
      background-color: #558ff0;
    
      svg {
        color: white;
      }
    }
  }
`;

class ActionGroupCell extends React.Component {
  renderContent = () => {
    const { actions } = this.props;

    return (
      <StyledContent>
        {actions.map((item, index) => {
          if (!item.show) {
            return '';
          }

          return (
            <a
              role={'button'}
              onClick={item.onClick}
              tabIndex={-1}
              key={index}
            >
              <div className="item">
                <img
                  src={item.icon}
                  alt="lock"
                />
                <span>{item.text}</span>
              </div>
            </a>
          );
        })}
      </StyledContent>
    );
  };

  renderTarget = (disabled) => (
    <StyledTarget className={disabled ? 'disabled' : ''}>
      <Icon icon={'more'} iconSize={15} />
    </StyledTarget>
  );

  render() {
    const { disabled } = this.props;

    return (
      <StyledContainer>
        <Popover
          content={this.renderContent()}
          target={this.renderTarget(disabled)}
          minimal
          popoverClassName={Classes.POPOVER_DISMISS}
        />
      </StyledContainer>
    );
  }
}

ActionGroupCell.propTypes = {
  actions: PropTypes.arrayOf(
    PropTypes.shape({
      icon: PropTypes.string.isRequired,
      show: PropTypes.bool,
      conClick: PropTypes.func,
    })
  ),
};

export default ActionGroupCell;
