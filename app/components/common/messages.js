import { defineMessages } from 'react-intl';


const scope = 'app.components.common';

export default defineMessages({
  optional: {
    id: `${scope}.optional`,
    defaultMessage: 'options',
  },
  submitError: {
    id: `${scope}.submitError`,
    defaultMessage: 'Please check again',
  },
  noDataFound: {
    id: `${scope}.noDataFound`,
    defaultMessage: 'No data found.',
  },
  noOptional: {
    id: `${scope}.noOptional`,
    defaultMessage: 'No options.',
  },
  tableLoading: {
    id: `${scope}.tableLoading`,
    defaultMessage: 'Loading...',
  },

  noOptionMessage: {
    id: `${scope}.noOptionMessage`,
    defaultMessage: 'Not found',
  },

  selectMessage: {
    id: `${scope}.selectMessage`,
    defaultMessage: 'Please select',
  },
  inputMessage: {
    id: `${scope}.inputMessage`,
    defaultMessage: 'Enter',
  },
  update: {
    id: `${scope}.update`,
    defaultMessage: 'Update',
  },
  readMore: {
    id: `${scope}.readMore`,
    defaultMessage: 'See More',
  },
  toggleOn: {
    id: `${scope}.toggleOn`,
    defaultMessage: 'On',
  },
  toggleOff: {
    id: `${scope}.toggleOff`,
    defaultMessage: 'Off',
  },
});
