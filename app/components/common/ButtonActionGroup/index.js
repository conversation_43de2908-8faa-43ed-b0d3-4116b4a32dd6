import React from 'react';
import styled, { css } from 'styled-components';
import isEmpty from 'lodash/isEmpty';
import { Classes, Popover, PopoverInteractionKind, Position, Button as ButtonBluePrint } from '@blueprintjs/core';
import * as PropTypes from 'prop-types';
import { backgroundColorButton } from '../../../styles/commonCss';


const DropdownButtons = styled.ul`
  list-style-type: none;
  margin-bottom: 0;
  padding: 0;
  text-align: left;
  min-width: ${(props) => props.minWidth ? `${props.minWidth}px` : 'auto'};

  li {
    padding: 0 10px;
    height: 50px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: 0.15s;

    &:hover {
      background-color: ${(props) => props.theme.colors.gray60} !important;
    }

    a {
      text-decoration: none;
      color: ${(props) => props.theme.colors.black};
      font-weight: ${(props) => props.theme.fontWeights.strong300};
      font-size: ${(props) => props.theme.fontSizes.small};
      line-height: 50px;
      width: 100%;
    }

    .image {
      margin-right: 10px;
    }
    
    &:nth-child(odd) {
      background-color: ${(props) => props.theme.colors.gray10};
    }
  }
`;

const StyledContainer = styled(ButtonBluePrint)`
  &.min-width-100 {
    min-width: 100px;
  }

  &.min-width-200 {
    min-width: 200px;
  }

  &.min-width-300 {
    min-width: 300px;
  }

  &.min-width-400 {
    min-width: 400px;
  }

  &.margin-button-15{
    margin: 0 15px;
  }

  &.bp3-button {
    transition: 0.15s; 
    font-weight: 300;
    border-radius: 30.5px;  
    box-shadow: none !important;
    text-transform: uppercase;

    font-size: ${(props) => props.theme.fontSizes.small};
    padding: 8px 16px;
    height: auto;
    
    border: 1px solid #d8d8d8;    
    color: ${(props) => props.theme.colors.black900};
    background-image: none;
    ${(props) => backgroundColorButton(props.theme.colors.gray20)}
    
    ${(props) => props.additionalCss}

    &.bp3-disabled {
      &:hover {
        outline: none;
        -webkit-box-shadow: none;
        box-shadow: none;
        background-color: rgba(206, 217, 224, 0.5);
        background-image: none;
        cursor: not-allowed;
        color: rgba(92, 112, 128, 0.5);
      }
    }
  }
`;

class ButtonActionGroup extends React.Component {
  render() {
    const { children, onClick, actions, dropdownOptions } = this.props;

    const additionalCss = css`
        background-image: linear-gradient(100deg, #41B652, #125f2a);
        color: #ffffff;
        border: none;
        &:active {
          background-color: transparent;
          background-image: linear-gradient(100deg, #41B652, #125f2a);
        }
      `;

    const content = (
      <DropdownButtons {...dropdownOptions}>
        {actions.map((option, idx) => (
          <li
            key={`btnCreate-${idx}`}
          >
            <a
              className="link"
              onClick={option.onClick}
              role={'button'}
              tabIndex={-1}
            >
              {/* {*/}
              {/*  option.icon &&*/}
              {/*  (<img*/}
              {/*    className="image"*/}
              {/*    src={option.icon}*/}
              {/*    width="14"*/}
              {/*    height="14"*/}
              {/*    alt="add"*/}
              {/*  />)*/}
              {/* }*/}
              {option.text}
            </a>
          </li>
        ))}
      </DropdownButtons>
    );

    return (
      <StyledContainer
        additionalCss={additionalCss}
      >
        <Popover
          minimal
          content={content}
          interactionKind={PopoverInteractionKind.HOVER}
          position={Position.BOTTOM}
          popoverClassName={Classes.POPOVER_DISMISS}
        >
          <a
            tabIndex={0}
            role={'button'}
            className="btn-action-group"
            onClick={onClick || ((e) => e.preventDefault())}
            style={{ color: 'white' }}
          >{children}</a>
        </Popover>
      </StyledContainer>
    );
  }
}


ButtonActionGroup.propTypes = {
  text: PropTypes.string,
  onClick: PropTypes.func,
  actions: PropTypes.arrayOf(
    PropTypes.shape({
      onClick: PropTypes.func,
      text: PropTypes.string,
      to: PropTypes.string,
    })
  ),
};

export default ButtonActionGroup;

