import styled from 'styled-components';
import FormGroup from '../../FormGroup';


export default styled(FormGroup)`
  border: none !important;
  width: 100%;
  height: auto;
  position: relative;
  
  .label {
    font-size: ${(props) => props.theme.fontSizes.small};
    font-weight: ${(props) => props.theme.fontWeights.normal};
    color: ${(props) => props.theme.colors.black900};
    margin-bottom: 5px;
    min-height: 24px;
  }
  
  .file-name { 
    font-size: ${(props) => props.theme.fontSizes.normal};
    font-weight: ${(props) => props.theme.fontWeights.strong};
    color: ${(props) => props.theme.colors.black30};
    margin-right: 20px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
  }
  
  .btn-upload {
    text-decoration: underline;
    color: ${(props) => props.theme.colors.blue700};
    font-size: ${(props) => props.theme.fontSizes.small};
    font-weight: ${(props) => props.theme.fontWeights.strong500};
  }
`;
