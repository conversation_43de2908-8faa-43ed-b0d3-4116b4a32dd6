import React from 'react';
import Wrapper from './styled/Wrapper';
import { injectIntl } from 'react-intl';
import ErrorMessage from '../ErrorMessage';
import isEmpty from 'lodash/isEmpty';
import FileName from './FileName';
import FormLabel from '../FormLabel';
import UploadTextFile from '../UploadTextFile';
import messages from '../../../containers/BorrowingPage/Step4Personal/messages';
import { uploadFamilyAddress } from '../../../services/user.service';
import { CommonToaster } from '../../CommonToaster';
import { getErrorMessageFromError } from '../../../constants/responseCode/utils';
import { Intent } from '@blueprintjs/core';
import { Col } from 'reactstrap';

class ViewTextFile extends React.Component { // eslint-disable-line react/prefer-stateless-function
  getRealFileName = (fileName) => {
    let result = fileName;

    const fragment = fileName.split('.');
    if (fragment.length > 2) {
      fragment.splice(0, 1);
      result = fragment.join('.');
    }

    return result;
  }

  renderFileName = () => {
    const { value } = this.props;

    if (isEmpty(value)) {
      return null;
    }

    if (Array.isArray(value)) {
      return value.map((item, index) => {
        const fileName = item ? item.split('/').pop() : '';

        return (
          <FileName
            key={index}
            href={item}
            fileName={this.getRealFileName(fileName)}
          />
        );
      });
    }

    const fileName = value ? value.split('/').pop() : '';

    return (
      <FileName
        href={value}
        fileName={this.getRealFileName(fileName)}
      />
    );
  }

  render() {
    const { label, value, emptyLabel } = this.props;

    // return (
    //   <Wrapper>
    //     {label && <FormLabel>{label}:</FormLabel>}
    //
    //     {this.renderFileName()}
    //   </Wrapper>
    // );

    return (
      <UploadTextFile
        readonly
        multiple
        label={label}
        value={value}
        emptyLabel={emptyLabel}
      />
    );
  }
}

export default injectIntl(ViewTextFile);
