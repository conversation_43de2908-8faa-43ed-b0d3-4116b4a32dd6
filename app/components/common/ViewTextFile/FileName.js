import React from 'react';
import styled from 'styled-components';

const StyledComponent = styled.div`
  position: relative;  
  border: 1px solid #d8d8d8;
  padding: 3px;
  max-width: 340px;
  
  .file-name {
    font-size: 12px !important;  
    opacity: 0.8;
  }
`;

const FileName = (props) => {
  const { fileName, href } = props;

  return (
    <StyledComponent>
      <a target="_blank" rel="noopener noreferrer" href={href} className="file-name">{fileName}</a>
    </StyledComponent>
  );
};

export default FileName;
