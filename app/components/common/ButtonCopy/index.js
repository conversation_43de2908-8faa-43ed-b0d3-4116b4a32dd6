import React, {Fragment} from 'react';
import styled, {css} from 'styled-components';
import {injectIntl} from 'react-intl';
import messages from '../messages';
import {CopyToClipboard} from 'react-copy-to-clipboard';
import Button from '../Button';
import {compose} from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import OpenVpnIcon from 'images/addon/open_vpn.png';
import WireGuardIcon from 'images/addon/wire_guard.png';

const StyledComponent = styled.div`
  display: flex;
  flex-direction: row;
  align-items: stretch;
  background-color: #E8ECEF;
  margin: 1px;
  font-size: 12px;
  font-weight: 420;
  justify-content: space-between;

  .title {
    min-width: 60px;
    border-left: 1px solid #c6c6c6;
    border-top: 1px solid #bcc0c2;
    border-bottom: 1px solid #bcc0c2;

    padding-top: 5px;
  }

  .area {
    padding: 7px 5px 2px 5px;
    min-width: 200px;
    text-align: left;

    border-top: 1px solid #bcc0c2;
    border-bottom: 1px solid #bcc0c2;
    border-left: 1px solid #c6c6c6;
  }

  .bp3-button {
    padding: 7px 9px 7px 9px;
    background-color: #6C757F;
    font-weight: bold;
    color: #FEFEFE;
    border-radius: 0px;
    margin: 0;
    border: 1px solid #bcc0c2;
  }

  .fa-copy {
    font-size: 12px;
  }

`;

class ButtonCopy extends React.Component {
  render() {
    const {
      title,
      content,
      className,
      style,

      isBtnCopy1,
      copyValue,
      btnText,

      isBtnCopy2,
      copyValue2,
      btnText2,

      isBtnSync,
      isSpin,
      onClick,

      isVpn,
      onDownloadVpn,
      onChangeVpn,
    } = this.props;

    if (content === null || content === '') {
      return '';
    }

    return (
      <StyledComponent>
        {title && <div className="title">
          <div className={className} style={style}>{title}</div>
        </div>}
        <div className="area">
          <div className={className} style={style}>{content}</div>
        </div>
        {isBtnCopy1 && <CopyToClipboard
          text={copyValue}
          onCopy={() => this.props.handleAlertSuccess('Copy to clipboard successful!')}
        >
          <Button
            small
            type="button"
            style={{fontSize: 10}}
            title={btnText}
          ><i className="fa fa-copy"/></Button>
        </CopyToClipboard>}
        {isBtnCopy2 && <CopyToClipboard
          text={copyValue2}
          onCopy={() => this.props.handleAlertSuccess('Copy to clipboard successful!')}
        >
          <Button
            small
            type="button"
            style={{fontSize: 10}}
            title={btnText2}
          ><i className="fa fa-copy"/></Button>
        </CopyToClipboard>
        }

        {isBtnSync && <CopyToClipboard
          text={''}
          onCopy={() => onClick()}
          title={'Change Ip'}
        >
          <Button
            small
            type="button"
            style={{fontSize: 10}}
          ><i className={`fa fa-sync ${isSpin ? 'fa-spin' : ''}`}/></Button>
        </CopyToClipboard>}


        {isVpn && <Fragment><CopyToClipboard
          text={''}
          onCopy={() => onDownloadVpn()}
          title={'Download File'}
        >
          <Button
            small
            type="button"
            style={{fontSize: 10}}
          ><i className={`fa fa-download ${isSpin ? 'fa-spin' : ''}`}/></Button>
        </CopyToClipboard>
          <CopyToClipboard
            text={''}
            onCopy={() => onChangeVpn()}
            title={`Change to ${content === 'OPEN_VPN' ? 'WireGuard' : 'OpenVpn'}`}
          >
            <Button
              small
              type="button"
              style={{padding: 0}}
            > <img src={content === 'OPEN_VPN' ? WireGuardIcon : OpenVpnIcon} height="30" width="30"/></Button>
          </CopyToClipboard>
        </Fragment>
        }

      </StyledComponent>
    );
  }
}

export default compose(
  WithHandleAlert,
  injectIntl,
)(ButtonCopy);
