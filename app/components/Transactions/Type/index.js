import React from 'react';
import { FormattedMessage, defineMessages } from 'react-intl';

import types from 'enums/ETransactionType';

const scope = 'app.common.Transaction';
const commonScope = 'app.common';

const messages = defineMessages({
  typeBorrowing: {
    id: `${scope}.typeBorrowing`,
    defaultMessage: 'Vay ngân hàng',
  },
  typeSavingDeposit: {
    id: `${scope}.typeSavingDeposit`,
    defaultMessage: 'Gửi tiết kiệm',
  },
  typeBorrowingP2P: {
    id: `${scope}.typeBorrowingP2P`,
    defaultMessage: 'Vay P2P',
  },
  typeLendingP2P: {
    id: `${scope}.typeLendingP2P`,
    defaultMessage: 'Cho vay P2P',
  },
  typeConsultant: {
    id: `${scope}.typeConsultant`,
    defaultMessage: 'Tư vấn',
  },
  unmatched: {
    id: `${commonScope}.unmatched`,
    defaultMessage: 'Unmatched',
  },
});

export const matchMessage = (type) => {
  switch (type) {
    case types.LOAN:
      return messages.typeBorrowing;
    case types.SAVING_DEPOSIT:
      return messages.typeSavingDeposit;
    case types.BORROWING_P2P:
      return messages.typeBorrowingP2P;
    case types.LENDING_P2P:
      return messages.typeLendingP2P;
    case types.CONSULTANT:
      return messages.typeConsultant;
    default:
      return messages.unmatched;
  }
};

const Type = (props) => {
  const { type } = props;
  const message = matchMessage(type);
  return (
    <span>
      <FormattedMessage {...message} />
    </span>
  );
};

export default Type;
