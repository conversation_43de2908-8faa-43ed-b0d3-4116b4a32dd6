import request from '../../utils/request';
import env from 'env';

export function getMailTemplates(data) {
  const requestURL = `${env.API_URL}/admin/mail-templates/list`;

  return request(requestURL, { method: 'POST', body: data });
}

export function updateMailTemplate(id, data) {
  const requestURL = `${env.API_URL}/admin/mail-templates/${id}`;

  return request(requestURL, { method: 'PUT', body: data });
}

export function insertMailTemplate(data) {
  const requestURL = `${env.API_URL}/admin/mail-templates`;

  return request(requestURL, { method: 'POST', body: data });
}

export function sendNotifyEmail(data) {
  const requestURL = `${env.API_URL}/admin/mail-templates/send-notify-email`;

  return request(requestURL, { method: 'POST', body: data });
}
