import request from '../../utils/request';
import env from 'env';

export function getPromotions(data) {
  const requestURL = `${env.API_URL}/promotions/list`;

  return request(requestURL, { method: 'POST', body: data });
}

export function getPromotion(id) {
  const requestURL = `${env.API_URL}/promotions/${id}`;

  return request(requestURL, { method: 'GET' });
}

export function createPromotion(data) {
  const requestURL = `${env.API_URL}/promotions`;

  return request(requestURL, { method: 'POST', body: data });
}

export function updatePromotion(id, data) {
  const requestURL = `${env.API_URL}/promotions/${id}`;

  return request(requestURL, { method: 'PUT', body: data });
}

export function deletePromotion(id) {
  const requestURL = `${env.API_URL}/promotions/${id}`;

  return request(requestURL, { method: 'DELETE' });
}
