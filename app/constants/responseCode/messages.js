import { defineMessages } from 'react-intl';
import { errorCode } from './index';

const scope = 'rb.responseCode';

export default defineMessages({
  [errorCode.UNAUTHORIZED]: {
    id: `${scope}.${errorCode.UNAUTHORIZED}`,
    defaultMessage: 'The account session has expired.',
  },
  [errorCode.TOO_MANY_REQUESTS]: {
    id: `${scope}.${errorCode.TOO_MANY_REQUESTS}`,
    defaultMessage: 'Acting too fast in a short amount of time',
  },
  [errorCode.LOCKED_WHEN_REACH_MAXIMUM_LOGIN_RETRY]: {
    id: `${scope}.${errorCode.LOCKED_WHEN_REACH_MAXIMUM_LOGIN_RETRY}`,
    defaultMessage: 'Action is locked. Please try again in 5 minutes',
  },
  [errorCode.LOCKED_IP_ONE_DAY]: {
    id: `${scope}.${errorCode.LOCKED_IP_ONE_DAY}`,
    defaultMessage: 'You have violated the privacy policy. Please contact <PERSON><PERSON> for assistance.',
  },
  [errorCode.SOMETHINGS_WENT_WRONG]: {
    id: `${scope}.${errorCode.SOMETHINGS_WENT_WRONG}`,
    defaultMessage: 'An error occurred during processing. Please contact Admin for assistance.',
  },
  [errorCode.LOGGED_FAILED]: {
    id: `${scope}.${errorCode.LOGGED_FAILED}`,
    defaultMessage: 'Login information is incorrect or has been suspended. Please contact Customer Service!',
  },
  [errorCode.REGISTER_FAILED]: {
    id: `${scope}.${errorCode.REGISTER_FAILED}`,
    defaultMessage: 'Invalid email information for registration!',
  },
});
