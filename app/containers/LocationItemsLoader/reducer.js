import { fromJS } from 'immutable';
import { STORE_ALL_PROVINCES, STORE_DISTRICTS, STORE_WARDS } from './constants';

const initialState = fromJS({
  provinces: [], // Just an array/list of provinces
  districts: {}, // A map of province id and its districts
  wards: {}, // A map of district id and its wards
});

function locationItemsReducer(state = initialState, action) {
  const { type, payload } = action;
  switch (type) {
    case STORE_ALL_PROVINCES:
      return state.set('provinces', fromJS(payload.provinces));
    case STORE_DISTRICTS:
      return state.setIn(['districts', payload.provinceId], fromJS(payload.districts));
    case STORE_WARDS:
      return state.setIn(['wards', payload.districtId], fromJS(payload.wards));
    default:
      return state;
  }
}

export default locationItemsReducer;
