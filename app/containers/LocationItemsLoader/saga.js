import logger from 'logger';
import { all, call, fork, put, take, takeLatest } from 'redux-saga/effects';
import {
  getAllProvinces,
  getWards,
  getDistricts,
} from './../../services/locationitems.service';
import {
  FETCH_ALL_PROVINCES,
  FETCH_DISTRICTS,
  FETCH_WARDS,
  STORE_DISTRICTS,
  STORE_WARDS,
  STORE_ALL_PROVINCES,
} from './constants';

function* fetchAllProvinces() {
  try {
    const result = yield call(getAllProvinces);
    const { data: provinces } = result;

    // Put to store
    yield put({ type: STORE_ALL_PROVINCES, payload: { provinces } });
  } catch (error) {
    logger.error(error);
  }
}

function* fetchDistricts(action) {
  const { payload } = action;
  const { provinceId } = payload;

  try {
    const result = yield call(getDistricts, provinceId);
    const { data: districts } = result;

    // Put to store
    yield put({ type: STORE_DISTRICTS, payload: { provinceId, districts } });
  } catch (error) {
    logger.error(error);
  }
}

function* fetchWards(action) {
  const { payload } = action;
  const { districtId } = payload;

  try {
    const result = yield call(getWards, districtId);
    const { data: wards } = result;

    // Put to store
    yield put({ type: STORE_WARDS, payload: { districtId, wards } });
  } catch (error) {
    logger.error(error);
  }
}
export default function* defaultSaga() {
  yield all([
    takeLatest(FETCH_ALL_PROVINCES, fetchAllProvinces),
    takeLatest(FETCH_DISTRICTS, fetchDistricts),
    takeLatest(FETCH_WARDS, fetchWards),
  ]);
}
