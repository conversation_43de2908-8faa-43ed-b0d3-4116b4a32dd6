import { FETCH_ALL_PROVINCES, FETCH_DISTRICTS, FETCH_WARDS } from './constants';

export function fetchAllProvinces() {
  return { type: FETCH_ALL_PROVINCES };
}

export function fetchDistricts(
  provinceId,
  callbacks = {
    onSuccess() {},
    onFailure() {},
  }
) {
  return {
    type: FETCH_DISTRICTS,
    payload: {
      provinceId,
    },
    callbacks,
  };
}

export function fetchWards(
  districtId,
  callbacks = {
    onSuccess() {},
    onFailure() {},
  }
) {
  return {
    type: FETCH_WARDS,
    payload: {
      districtId,
    },
    callbacks,
  };
}
