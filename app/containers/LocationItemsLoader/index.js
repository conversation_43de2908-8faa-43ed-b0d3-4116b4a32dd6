import React from 'react';
import reducer from './reducer';
import saga from './saga';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import makeSelectLocationItemsLoader from './selectors';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { fetchAllProvinces, fetchDistricts, fetchWards } from './actions';

/**
 * This component does not try to load all records of districts and wards at once.
 * It, at first, loads all provinces.
 * Its children will decide which districts of a particular province need to be loaded via {loadDistrictsOfParticularProvince}.
 * Its children will decide which wards of a particular district need to be loaded via {loadWardsOfParticularDistrict}.
 * These two functions are passed to children.
 */
class LocationItemsLoader extends React.Component {
  state = {
    selectedProvinceId: null,
    selectedDistrictId: null,
  };

  componentWillMount() {
    const { dispatch, provinces } = this.props;

    if (!provinces || provinces.isEmpty()) {
      dispatch(fetchAllProvinces());
    }
  }

  loadDistrictsOfParticularProvince = (provinceId) => {
    const { dispatch } = this.props;

    this.setState({ selectedProvinceId: provinceId });

    dispatch(fetchDistricts(provinceId));
  };

  loadWardsOfParticularDistrict = (districtId) => {
    const { dispatch } = this.props;

    this.setState({ selectedDistrictId: districtId });

    dispatch(fetchWards(districtId));
  };

  cloneChild = (child) => {
    const { provinces, districts, wards } = this.props;
    const { selectedProvinceId, selectedDistrictId } = this.state;

    const provinceArray = provinces.toJS();

    let districtArray = [];
    if (selectedProvinceId) {
      const districtList = districts.get(selectedProvinceId);
      if (districtList) {
        districtArray = districtList.toJS();
      }
    }

    let wardArray = [];
    if (selectedDistrictId) {
      const wardList = wards.get(selectedDistrictId);
      if (wardList) {
        wardArray = wardList.toJS();
      }
    }

    return React.cloneElement(child, {
      loadDistrictsOfParticularProvince: this.loadDistrictsOfParticularProvince,
      loadWardsOfParticularDistrict: this.loadWardsOfParticularDistrict,
      provinces: provinceArray,
      relevantDistricts: districtArray,
      relevantWards: wardArray,
    });
  };

  render() {
    const { children } = this.props;

    const clonedChildren = React.Children.map(children, this.cloneChild);

    return <React.Fragment>{clonedChildren}</React.Fragment>;
  }
}

const mapStateToProps = makeSelectLocationItemsLoader();

const withConnect = connect(mapStateToProps);
const withReducer = injectReducer({ key: 'LocationItemsLoader', reducer });
const withSaga = injectSaga({ key: 'LocationItemsLoader', saga });

export default compose(
  withReducer,
  withSaga,
  withConnect
)(LocationItemsLoader);
