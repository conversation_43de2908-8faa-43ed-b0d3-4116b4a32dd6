import { createSelector } from 'reselect';

/**
 * Direct selector to the LocationItemsLoader state domain
 */
const selectLocationItemsLoader = (state) => state.get('LocationItemsLoader');

/**
 * Other specific selectors
 */

/**
 * Default selector used by LocationItemsLoader
 */

const makeSelectLocationItemsLoader = () =>
  createSelector(
    selectLocationItemsLoader,
    (substate) => ({
      provinces: substate.get('provinces'),
      districts: substate.get('districts'),
      wards: substate.get('wards'),
    })
  );

export default makeSelectLocationItemsLoader;
export { selectLocationItemsLoader };
