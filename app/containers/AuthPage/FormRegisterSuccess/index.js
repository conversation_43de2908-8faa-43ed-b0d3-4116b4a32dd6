import React from 'react';
import * as PropTypes from 'prop-types';

import messages from './messages';
import imgRegisterSuccessClient from '../../../images/formIcon/registerSuccess.svg';
import Button from '../../../components/common/Button';
import { forwardTo } from '../../../utils/history';
import styled from 'styled-components';
import Container from 'reactstrap/es/Container';
import Row from 'reactstrap/es/Row';
import Col from 'reactstrap/es/Col';
import { injectIntl } from 'react-intl';
import { verifyAcc } from 'services/user.service';

const Wrapper = styled.div`
  padding: 100px 0;
  display: flex;
  flex-direction: column;
  
  .message {
    font-size: 25px;
    color: ${(props) => props.theme.colors.black};
    font-weight: ${(props) => props.theme.fontWeights.strong500};
    opacity: 1;
    margin: 60px 0 40px;
  }
  
  img {
    height: 250px;
  }
`;


class FormRegisterSuccess extends React.Component {
  componentWillMount() {
    this.loadInitData();
  }

  loadInitData = async () => {
    const queryParams = new URLSearchParams(window.location.search);
    const code = queryParams.get('code');

    await verifyAcc(code);
  }

  render() {
    const { intl } = this.props;

    return (
      <Container>
        <Row>
          <Col md={{ size: 8, offset: 2 }}>
            <Wrapper>
              <img src={imgRegisterSuccessClient} alt={''} />
              <div className="message text-center">{intl.formatMessage(messages.completedMessage)}</div>
              <div className="text-center">
                <Button onClick={() => forwardTo('/login')}>
                  {intl.formatMessage(messages.clientBtn)}
                </Button>
              </div>
            </Wrapper>
          </Col>
        </Row>
      </Container>
    );
  }
}

FormRegisterSuccess.propTypes = {
  userType: PropTypes.string,
  userStatus: PropTypes.string,
};

export default injectIntl(FormRegisterSuccess);
