import { defineMessages } from 'react-intl';

const scope = 'app.containers.AuthPage.FormRegister';

export default defineMessages({
  new_pass_title: {
    id: `${scope}.new_pass_title`,
    defaultMessage: 'Create a new password',
  },
  new_password: {
    id: `${scope}.new_password`,
    defaultMessage: 'Enter your new password',
  },
  current_password: {
    id: `${scope}.current_password`,
    defaultMessage: 'Enter old password',
  },
  confirm_new_password: {
    id: `${scope}.confirm_new_password`,
    defaultMessage: 'Confirm password',
  },
  submit: {
    id: `${scope}.submit`,
    defaultMessage: 'Submit',
  },
  emailRequiredError: {
    id: `${scope}.emailRequiredError`,
    defaultMessage: 'Please enter email.',
  },
  passwordRequiredError: {
    id: `${scope}.passwordRequiredError`,
    defaultMessage: 'Please enter a password.',
  },
  passwordMaxLengthError: {
    id: `${scope}.passwordMaxLengthError`,
    defaultMessage: 'Please enter a password less than 15 characters.',
  },
  passwordMinLengthError: {
    id: `${scope}.passwordMinLengthError`,
    defaultMessage: 'Please enter more than 8 characters.',
  },
  confirm_passwordRequiredError: {
    id: `${scope}.confirm_passwordRequiredError`,
    defaultMessage: 'Please re-enter password.',
  },
  confirm_passwordMaxLengthError: {
    id: `${scope}.confirm_passwordMaxLengthError`,
    defaultMessage: 'Please enter authentication password less than 15 characters.',
  },
  passwordNotMatchError: {
    id: `${scope}.passwordNotMatchError`,
    defaultMessage: 'Authentication password does not match.',
  },
  passwordStrongError: {
    id: `${scope}.passwordStrongError`,
    defaultMessage: 'Password must be between 8 and 15 characters, with at least 1 letter and number.',
  },
  passwordPlaceHolder: {
    id: `${scope}.passwordPlaceHolder`,
    defaultMessage: 'Password must be between 8 and 15 characters, with at least 1 alphanumeric',
  },
});
