import { defineMessages } from 'react-intl';

const scope = 'app.containers.AuthPage';

export default defineMessages({
  info1: {
    id: `${scope}.info1`,
    defaultMessage: 'System Provision',
  },
  info2: {
    id: `${scope}.info2`,
    defaultMessage: 'Quality proxy with the best price in the market.',
  },
  username: {
    id: `${scope}.username`,
    defaultMessage: 'Username',
  },
  email: {
    id: `${scope}.email`,
    defaultMessage: 'Email',
  },
  maxLengthError: {
    id: `${scope}.maxLengthError`,
    defaultMessage: 'Please enter less than {length} characters',
  },
  minLengthError: {
    id: `${scope}.passwordMinLength`,
    defaultMessage: 'Please enter greater than {length} characters',
  },
  phoneNumber: {
    id: `${scope}.phoneNumber`,
    defaultMessage: 'Phone number',
  },
  enterPhoneNumber: {
    id: `${scope}.enterPhoneNumber`,
    defaultMessage: 'Enter your phone number',
  },
  enterUsername: {
    id: `${scope}.enterUsername`,
    defaultMessage: 'Enter username',
  },
  enterEmail: {
    id: `${scope}.enterEmail`,
    defaultMessage: 'Enter email',
  },
  phoneNumberError: {
    id: `${scope}.phoneNumberError`,
    defaultMessage: 'Please enter the phone number',
  },
  password: {
    id: `${scope}.password`,
    defaultMessage: 'Password',
  },
  enterPassword: {
    id: `${scope}.enterPassword`,
    defaultMessage: 'Enter password',
  },
  passwordError: {
    id: `${scope}.passwordError`,
    defaultMessage: 'Please enter a password',
  },
  confirmPassword: {
    id: `${scope}.confirmPassword`,
    defaultMessage: 'Confirm password',
  },
  confirmPasswordError: {
    id: `${scope}.confirmPasswordError`,
    defaultMessage: 'Please enter Confirm Password',
  },
  otpCode: {
    id: `${scope}.otpCode`,
    defaultMessage: 'OTP code',
  },
  enterOtpCode: {
    id: `${scope}.enterOtpCode`,
    defaultMessage: 'Enter OTP code',
  },
  otpCodeError: {
    id: `${scope}.otpCodeError`,
    defaultMessage: 'Please enter OTP code',
  },
  passwordPlaceHolder: {
    id: `${scope}.passwordPlaceHolder`,
    defaultMessage: 'Password must be between 8 and 15 characters, with at least 1 letter and number',
  },
  register_agree: {
    id: `${scope}.register_agree`,
    defaultMessage: 'By register, you agree to our',
  },
  and: {
    id: `${scope}.and`,
    defaultMessage: 'and',
  },
  policy: {
    id: `${scope}.policy`,
    defaultMessage: 'Privacy Policy',
  },
});
