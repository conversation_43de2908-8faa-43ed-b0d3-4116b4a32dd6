/*
 *
 * AuthPage constants
 *
 */

export const ON_CHANGE = 'app/AuthPage/ON_CHANGE';
export const SUBMIT = 'app/AuthPage/SUBMIT';
export const SUBMIT_OTP = 'app/AuthPage/SUBMIT_OTP_REGISTER';
export const SUBMIT_FORGOT_PASSWORD_OTP = 'app/AuthPage/SUBMIT_FORGOT_PASSWORD_OTP';
export const SUBMITTED_ERROR = 'app/AuthPage/SUBMITTED_ERROR';
export const GET_BANKS = 'app/AuthPage/GET_BANKS';
export const UPLOAD_AVATAR = 'app/AuthPage/USER_UPLOAD_AVATAR';
export const GET_PROVINCES = 'app/AuthPage/GET_PROVINCES';
export const UPLOAD_PHOTO_CARD = 'app/AuthPage/UPLOAD_PHOTO_CARD';
export const UPLOAD_CONTRACT_FILE = 'app/AuthPage/UPLOAD_CONTRACT_FILE';
export const INIT_DATA_REGISTER = 'app/AuthPage/INIT_DATA_REGISTER';

export const KEY_APP = 'authPage';
export const FORM_TYPE_LOGIN = 'login';
export const FORM_TYPE_REGISTER = 'register';
export const FORM_TYPE_FORGOT_PASSWORD = 'forgot-password';
export const FORM_TYPE_COMPLETE_PROFILE = 'complete-profile';
export const FORM_TYPE_CREATE_PASSWORD = 'create-password';
export const FORM_TYPE_REGISTER_SUCCESS = 'register-success';
export const FORM_TYPE_REGISTER_SUCCESS_UNCOMPLETE = 'register-success-uncomplete';
export const FORM_TYPE_CONFIRM_OTP = 'confirm-otp';

export const FORM_TYPE_LOGIN_VI = 'login';
export const FORM_TYPE_REGISTER_VI = 'register';
export const FORM_TYPE_FORGOT_PASSWORD_VI = 'forget-password';
export const FORM_TYPE_CREATE_PASSWORD_VI = 'create-password';
export const FORM_TYPE_REGISTER_SUCCESS_VI = 'register-success';
export const SHOW_PROXY_LOCATION = 'proxy-location';
