import React, { Fragment } from 'react';
import { injectIntl } from 'react-intl';
import messages from '../messages';
import { formatDataList, bytesToSize } from 'utils/utilHelper';
import TransactionList from 'components/Transactions/TransactionList';
import { getProxies } from 'services/admin/proxy.service';
import WithHandlePromise from 'containers/WithHandlePromise';
import { compose } from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import auth from 'utils/auth';
import { permission } from 'constants/permission';
import get from 'lodash/get';
import moment from 'moment';
import Button from 'components/common/Button';
import styled from 'styled-components';

const getColumns = ({
                      intl,
                      handleRemoveRow,
                    }) => (
  [
    {
      Header: intl.formatMessage(messages.no),
      accessor: 'index',
      headerClassName: 'table-header',
      width: 50,
    },
    {
      Header: 'Modem',
      accessor: 'id',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => {
        const val = row.original;
        return (<div style={{ display: 'flex', flexDirection: 'column', fontSize: 12 }}>
          <span style={{ textAlign: 'left' }}><i className="fa fa-desktop" title="Modem Name" /> {get(val, 'modem.name')}</span>
          <span style={{ textAlign: 'left' }}><i className="fa fa-globe" title="Domain" /> {get(val, 'modem.domain')}</span>
        </div>
        );
      },
      width: 170,
    },
    {
      Header: 'Customer',
      accessor: 'email',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => {
        const val = row.original;
        return (<div style={{ display: 'flex', flexDirection: 'column', fontSize: 12 }}>
          <span style={{ textAlign: 'left' }}><i className="fa fa-id-card" title="Customer Name" /> {get(val, 'license.customer.name')}</span>
          <span style={{ textAlign: 'left' }}><i className="fa fa-envelope" title="Customer Email" /> {get(val, 'license.customer.email')}</span>
        </div>
        );
      },
      width: 150,
    },
    {
      Header: 'Position',
      accessor: 'xproxyPosition',
      headerClassName: 'table-header',
      sortable: false,
      width: 70,
    },
    {
      Header: 'Shared Port',
      accessor: 'sharedPort',
      headerClassName: 'table-header',
      sortable: false,
      width: 120,
      Cell: (row) => {
        const val = row.original;
        const brotherPort = get(val, 'brotherPort', '');
        return (
          <span>{row.value}{brotherPort !== '' && brotherPort !== null ? `/${brotherPort}` : ''}</span>
        );
      } },
    {
      Header: 'Port Type',
      accessor: 'portType',
      headerClassName: 'table-header',
      sortable: false,
      width: 90,
    },
    {
      Header: 'Ul Bytes',
      accessor: 'counterUlUsedBytes',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => (
        <span>{bytesToSize(row.value)}</span>
      ),
    },
    {
      Header: 'Dl Bytes',
      accessor: 'counterDlUsedBytes',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => (
        <span>{bytesToSize(row.value)}</span>
      ),
    },
    {
      Header: 'All Bytes',
      accessor: 'counterAllUsedBytes',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => (
        <span>{bytesToSize(row.value)}</span>
      ),
    },
    {
      Header: 'Counter Updated',
      accessor: 'counterAllUpdated',
      headerClassName: 'table-header',
      sortable: false,
    },
    {
      Header: 'Authentication user',
      accessor: 'authenticationUsers',
      headerClassName: 'table-header',
      sortable: false,
      width: 130,
    },
    {
      Header: 'Authorization_ips',
      accessor: 'authorizationIps',
      headerClassName: 'table-header',
      sortable: false,
      width: 140,
    },
    {
      Header: 'Usage Status',
      accessor: 'saleStatus',
      headerClassName: 'table-header',
      sortable: false,
    },
    {
      Header: 'Status',
      accessor: 'status',
      headerClassName: 'table-header',
      sortable: false,
    },
    {
      Header: 'ISP',
      accessor: 'isp',
      headerClassName: 'table-header',
      sortable: false,
    },
    {
      Header: 'License',
      accessor: 'id',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => {
        const val = row.original;
        return (<span style={{ fontSize: 11 }}>{get(val, 'license.uuid')}</span>);
      },
      width: 250,
    },
    {
      Header: 'Action',
      accessor: 'uuid',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => {
        if (row.original.status === 'EXPIRED') { return ''; }
        const val = row.value;
        return (
          <Fragment>
            <div className="flex flex-column align-content-end action-btn">
              <div className="text-left">
                <Button
                  primary
                  small
                  onClick={() => { handleRemoveRow(val); }}
                  type="button"
                ><i className="fa fa-eraser" /> Delete</Button>
              </div>
            </div>
          </Fragment>
        );
      },
    },
  ]
);

const DEFAULT_PAGES = -1;

const StyledComponent = styled.div`
  .action-btn {
    .bp3-button {
      font-size: 9px;
      padding: 0 3px 0 3px;
      margin-bottom: 1px;
      width: 100%;
    }
  }
  
`;

class ModemList extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      isLoading: false,
      pages: DEFAULT_PAGES,
    };
  }

  fetchData = async (state) => {
    this.setState({ isLoading: true });
    const { handleUpdateModemList } = this.props;
    const { page, pageSize, sorted } = state;
    const requestBody = {
      filtered: this.toFilteredList(),
      pageSize,
      page,
      sorted,
    };

    this.props.handlePromise(getProxies(requestBody), (response) => {
      const { data, pages } = response.data;
      handleUpdateModemList(formatDataList(data, page, pageSize));
      this.setState({
        pages,
        isLoading: false,
      });
    });
  };

  toFilteredList = () => {
    const { filteredList } = this.props;
    return Object
      .entries(filteredList)
      .map((entry) => ({
        id: entry[0],
        value: Array.isArray(entry[1]) ? entry[1].join(',') : entry[1],
      }));
  };

  render() {
    const {
      intl,
      dataList,
      getKeyFromFilteredList,
      handleSelectRow,
      handleRemoveRow,
      associates,
    } = this.props;

    const { pages, isLoading } = this.state;
    const columns = getColumns({
      intl,
      handleSelectRow,
      handleRemoveRow,
      associates,
    });

    return (
      <StyledComponent>
        <TransactionList
          key={getKeyFromFilteredList()}
          manual
          data={dataList}
          pages={pages}
          loading={isLoading}
          columns={columns}
          onFetchData={this.fetchData}
          defaultSorted={[
            {
              id: 'createdDate',
              desc: true,
            },
          ]}
        />
      </StyledComponent>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl,
)(ModemList);
