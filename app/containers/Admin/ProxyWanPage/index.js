import React, { Fragment } from 'react';
import StyledContainer from 'containers/Admin/ProxyWanPage/styles';
import Card from 'components/Card';
import ProxyList from './ProxyList';
import FormInputGroup from 'components/common/FormInputGroup';
import { forwardTo } from '../../../utils/history';
import { routes } from 'containers/Routes/routeHelper';
import { injectIntl } from 'react-intl';
import { compose } from 'redux';
import messages from './messages';
import FilterDropdownGroupWrapper from 'components/FilterDropdownWrapper';
import { TO, convertDropdownList } from 'utils/utilHelper';
import DropdownList from 'components/DropdownList';
import AdvancedSearchPopup from './AdvancedSearchPopup';
import ConfirmDialog from 'components/common/ConfirmDialog';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import { getModems } from 'services/admin/modem.service';
import { deleteProxies } from 'services/admin/proxy.service';
import ButtonCreate from 'components/common/ButtonCreate';
import ImportPopup from './ImportPopup';
import { getProxySaleStatusOptions } from './utils';
import Button from 'components/common/Button';
import env from 'env';
import GeneratePopup from './GeneratePopup';

export class ProxyWanList extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      dataList: [],
      isOpenAdvancedSearch: false,
      selectedId: props.match.params.id,
      forceRefresh: false,
      filteredList: {
        name: '',
        license: '',
        status: '',
        publicIp: '',
        port: '',
        authUserName: '',
        authIps: '',
        saleStatus: '',
        modemId: '',
      },
      isConfirm: false,
      modems: [],
    };
  }

  componentWillReceiveProps(nextProps) {
  }

  componentWillMount() {
    this.loadInitData();
  }

  loadInitData = async () => {
    const modems = await getModems({
      filtered: [],
      pageSize: 100,
      page: 0,
    });
    this.setState({
      modems: modems.data.data,
    });
  }

  getKeyFromFilteredList = () => {
    const { filteredList, forceRefresh } = this.state;
    return `${filteredList.name}
            -${filteredList.license}
            -${filteredList.status}
            -${filteredList.publicIp}
            -${filteredList.port}
            -${filteredList.authUserName}
            -${filteredList.authIps}
            -${filteredList.saleStatus}
            -${filteredList.modemId}
            -${forceRefresh}`;
  }

  handleUpdateModemList = (data) => {
    this.setState({
      dataList: data,
    });
  }

  handleSelectRow = (id) => {
    forwardTo(`/admin/modem-detail/${id}`);
  };

  handleCloseDetails = () => () => {
    forwardTo(routes.ADMIN_MODEM);
  };

  forceRefresh = () => {
    this.setState({
      forceRefresh: !this.state.forceRefresh,
    });
  }

  handleSearch = (name, value) => {
    this.setState((prevState) => ({
      filteredList: {
        ...prevState.filteredList,
        [name]: value,
      },
    }));
  }

  handleMultiSearch = (payload) => {
    this.setState((prevState) => ({
      filteredList: {
        ...prevState.filteredList,
        ...payload,
      },
    }));
  }

  handleClearAllFilters = () => {
    this.setState({
      filteredList: {
        name: '',
        license: '',
        status: '',
        publicIp: '',
        port: '',
        authUserName: '',
        authIps: '',
        saleStatus: '',
        modemId: '',
      },
    });
  }

  handleAdvancedSearch = (isOpen = true) => {
    this.setState({
      isOpenAdvancedSearch: isOpen,
    });
  }

  handleRemoveRow = (id) => {
    this.setState({
      selectedId: id,
      isConfirm: true,
    });
  }

  handleCloseConfirmPopup = () => {
    this.setState({
      isConfirm: false,
    });
  }

  handleAcceptConfirmPopup = async () => {
    const { intl } = this.props;
    const { selectedId } = this.state;
    const requestBody = {
      uuids: [selectedId],
    };
    const [err, response] = await TO(deleteProxies(requestBody));
    if (err) {
      console.log(err);
    }

    if (response.data && response.data > 0) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.msgDeleteSuccess));
      this.forceRefresh();
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgDeleteFailed));
    }
    this.setState({
      isConfirm: false,
    });
  }

  togglePopupImport = (isOpenImport = true) => {
    this.setState({
      isOpenImport,
    });
  }

  togglePopup = (isOpen = true) => {
    this.setState({
      isOpenPopup: isOpen,
    });
  }

  handleExportExcel = () => {
    window.open(`${env.API_URL}/admin/proxies/export-manual-proxies`, '_blank');
  }

  render() {
    const { intl } = this.props;
    const { filteredList, dataList, isOpenAdvancedSearch, isConfirm, modems, isOpenImport, isOpenPopup } = this.state;
    const modemOptions = convertDropdownList(modems.map((i) => ({ name: i.name, id: i.uuid })), intl.formatMessage(messages.allLabel), '');
    const proxySaleStatusOptions = convertDropdownList(getProxySaleStatusOptions(intl), intl.formatMessage(messages.allLabel), '');

    return (
      <StyledContainer>
        <Fragment>
          <Card>
            <div className="margin-bottom-13 d-flex justify-content-between">
              <FilterDropdownGroupWrapper>
                <div className="row no-gutters min-width-100">
                  {/* <div className={'col-md-2'}>*/}
                  {/* <FormInputGroup*/}
                  {/* didCheckErrors={false}*/}
                  {/* label={intl.formatMessage(messages.search)}*/}
                  {/* name="name"*/}
                  {/* onChange={(e) => {*/}
                  {/* this.handleSearch('name', e.target.value);*/}
                  {/* }}*/}
                  {/* type={'text'}*/}
                  {/* value={filteredList.name}*/}
                  {/* placeholder={'Name'}*/}
                  {/* />*/}
                  {/* </div>*/}
                  <div className="col-2">
                    <DropdownList
                      label={'Modem'}
                      value={modemOptions.find((option) =>
                        option.value === filteredList.modemId
                      )}
                      options={modemOptions}
                      onChange={(option) => {
                        this.handleSearch('modemId', option.value);
                      }}
                    />
                  </div>
                  <div className="col-2">
                    <DropdownList
                      label={'Usage Status'}
                      value={proxySaleStatusOptions.find((option) =>
                        option.value === filteredList.saleStatus
                      )}
                      options={proxySaleStatusOptions}
                      onChange={(option) => {
                        this.handleSearch('saleStatus', option.value);
                      }}
                    />
                  </div>
                  {/* <div className="col-2">
                    <FormInputGroup
                      label="Public IP"
                      name="location"
                      onChange={(e) => {
                        this.handleSearch('publicIp', e.target.value);
                      }}
                      type={'text'}
                      value={filteredList.publicIp}
                    />
                  </div> */}
                  <div className="col-2">
                    <FormInputGroup
                      label={'License'}
                      name="license"
                      onChange={(e) => {
                        this.handleSearch('license', e.target.value);
                      }}
                      type={'text'}
                      value={filteredList.license}
                      placeholder={'License'}
                    />
                  </div>
                  <div className="clear-filter-button">
                    <div onClick={this.handleClearAllFilters}>
                      {intl.formatMessage(messages.clearAllFiltersButton)}
                    </div>
                  </div>
                  <div className="advance-search-button">
                    <div onClick={this.handleAdvancedSearch}>
                      {intl.formatMessage(messages.advancedSearchButton)} <i className="fa fa-caret-down" />
                    </div>
                  </div>
                </div>
              </FilterDropdownGroupWrapper>
              {env.PROXY_MANUAL ? (
                <ButtonCreate
                  onClick={() => this.togglePopupImport(true, '')}
                />) : ('')}
              <ButtonCreate
                onClick={() => this.togglePopup(true)}
              />
            </div>
            {env.PROXY_MANUAL ? (
              <div className="margin-bottom-13 d-flex justify-content-between">
                <div className="mt-2">
                  <Button
                    primary
                    small
                    onClick={() => this.handleExportExcel()}
                    type="button"
                    style={{ fontSize: 10 }}
                  ><i className="fa fa-file-excel" /> Export manual</Button>
                </div>
              </div>
            ) : ('')}
            <ProxyList
              dataList={dataList}
              filteredList={filteredList}
              handleUpdateModemList={this.handleUpdateModemList}
              getKeyFromFilteredList={this.getKeyFromFilteredList}
              handleSelectRow={this.handleSelectRow}
              handleRemoveRow={this.handleRemoveRow}
              forceRefresh={this.forceRefresh}
            />
          </Card>
          <AdvancedSearchPopup
            isOpen={isOpenAdvancedSearch}
            handleOnClose={() => this.handleAdvancedSearch(false)}
            filteredList={filteredList}
            handleMultiSearch={this.handleMultiSearch}
          />
        </Fragment>
        <ConfirmDialog
          message={intl.formatMessage(messages.messageConfirm)}
          title={intl.formatMessage(messages.titleConfirm)}
          isOpen={isConfirm}
          confirmButtonText={intl.formatMessage(messages.confirmButton)}
          cancelButtonText={intl.formatMessage(messages.cancelButton)}
          onConfirm={this.handleAcceptConfirmPopup}
          onClose={this.handleCloseConfirmPopup}
          focusCloseButton
        />
        <ImportPopup
          isOpen={isOpenImport}
          handleOnClose={() => this.togglePopupImport(false)}
          forceRefresh={this.forceRefresh}
        />
        <GeneratePopup
          isOpen={isOpenPopup}
          handleOnClose={() => this.togglePopup(false)}
          forceRefresh={this.forceRefresh}
          modems={modems}
        />
      </StyledContainer>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(ProxyWanList);
