import { defineMessages } from 'react-intl';

const scope = 'app.components.common';

export default defineMessages({
  search: {
    id: `${scope}.search`,
    defaultMessage: 'T<PERSON><PERSON> kiếm',
  },
  allLabel: {
    id: `${scope}.allLabel`,
    defaultMessage: 'Tất cả',
  },
  searchPlaceHolder: {
    id: `${scope}.searchPlaceHolder`,
    defaultMessage: 'Mã, Tên',
  },
  no: {
    id: `${scope}.no`,
    defaultMessage: 'STT',
  },
  agentCode: {
    id: `${scope}.agentCode`,
    defaultMessage: 'Mã đại lý',
  },
  shortName: {
    id: `${scope}.shortName`,
    defaultMessage: 'Tên viết tắt',
  },
  agentName: {
    id: `${scope}.agentName`,
    defaultMessage: 'Tên đại lý',
  },
  address: {
    id: `${scope}.address`,
    defaultMessage: 'Địa chỉ',
  },
  nation: {
    id: `${scope}.nation`,
    defaultMessage: 'Quốc gia',
  },
  serviceGroup: {
    id: `${scope}.serviceGroup`,
    defaultMessage: 'Nhóm đại lý',
  },
  associateGroup: {
    id: `${scope}.associateGroup`,
    defaultMessage: 'Hiệp hội',
  },
  contact: {
    id: `${scope}.contact`,
    defaultMessage: 'Người liên hệ',
  },
  phoneNumber: {
    id: `${scope}.phoneNumber`,
    defaultMessage: 'Số điện thoại',
  },
  status: {
    id: `${scope}.status`,
    defaultMessage: 'Trạng thái',
  },
  action: {
    id: `${scope}.action`,
    defaultMessage: 'Thao tác',
  },
  update: {
    id: `${scope}.update`,
    defaultMessage: 'Cập nhật',
  },
  create: {
    id: `${scope}.create`,
    defaultMessage: 'Tạo',
  },
  close: {
    id: `${scope}.close`,
    defaultMessage: 'Đóng',
  },
  requiredNotNull: {
    id: `${scope}.requiredNotNull`,
    defaultMessage: 'Vui lòng nhập thông tin',
  },
  msgUpdateSuccess: {
    id: `${scope}.msgUpdateSuccess`,
    defaultMessage: 'Cập nhật thành công',
  },
  msgUpdateFailed: {
    id: `${scope}.msgUpdateFailed`,
    defaultMessage: 'Cập nhật thất bại.',
  },
  msgCreateSuccess: {
    id: `${scope}.msgCreateSuccess`,
    defaultMessage: 'Thêm mới thành công',
  },
  msgCreateFailed: {
    id: `${scope}.msgCreateFailed`,
    defaultMessage: 'Thêm mới thất bại',
  },
  msgDeleteSuccess: {
    id: `${scope}.msgDeleteSuccess`,
    defaultMessage: 'Xóa dữ liệu thành công',
  },
  msgDeleteFailed: {
    id: `${scope}.msgDeleteFailed`,
    defaultMessage: 'Không thể xóa dữ liệu này',
  },
  notFound: {
    id: `${scope}.notFound`,
    defaultMessage: 'Không tìm thấy dữ liệu',
  },
  clearAllFiltersButton: {
    id: `${scope}.clearAllFiltersButton`,
    defaultMessage: 'Xóa bộ lọc',
  },
  advancedSearchButton: {
    id: `${scope}.advancedSearchButton`,
    defaultMessage: 'Tìm kiếm nâng cao',
  },
  titleConfirm: {
    id: `${scope}.titleConfirm`,
    defaultMessage: 'Thông báo xác nhận',
  },
  messageConfirm: {
    id: `${scope}.messageConfirm`,
    defaultMessage: 'Bạn có muốn xóa dữ liệu này ?',
  },
  confirmButton: {
    id: `${scope}.confirmButton`,
    defaultMessage: 'Xác nhận',
  },
  cancelButton: {
    id: `${scope}.cancelButton`,
    defaultMessage: 'Hủy bỏ',
  },
  addLabel: {
    id: `${scope}.addLabel`,
    defaultMessage: 'Tạo mới',
  },
  add: {
    id: `${scope}.add`,
    defaultMessage: 'Create',
  },
  edit: {
    id: `${scope}.edit`,
    defaultMessage: 'Update',
  },
});
