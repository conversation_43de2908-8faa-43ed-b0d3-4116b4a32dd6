import { eModemStatus } from 'enums/EModemStatus';
import { eModemType } from 'enums/EModemType';

export const getModemStatusOptions = (intl) => [
  {
    name: eModemStatus.READY,
    id: eModemStatus.READY,
  },
  {
    name: eModemStatus.PAUSE,
    id: eModemStatus.PAUSE,
  },
  {
    name: eModemStatus.STOP,
    id: eModemStatus.STOP,
  },
];

export const getModemTypeOptions = (intl) => [
  {
    name: eModemType.MOBILE,
    id: eModemType.MOBILE,
  },
];

export const configCategory = {
  STRIPE: [
    {
      key: 'STRIPE_WEBHOOK_SECRET_KEY',
      name: 'Webhook Secret key',
    }, {
      key: 'STRIPE_API_SECRET_KEY',
      name: 'Api secret key',
    },
    {
      key: 'STRIPE_ENABLE',
      name: 'Enable',
    },
  ],
  COMMON: [
    {
      key: 'AFFILIATE_COMMISSION_RATE',
      name: 'Affiliate commission rate',
    }, {
      key: 'NOTIFICATION_AVAILABLE_PROXY',
      name: 'Notification Available Proxy',
    },
  ],
  NOW_PAYMENTS: [
    {
      key: 'NOW_PAYMENTS_API_KEY',
      name: 'Api key',
    },
  ],
  ADMIN_NOTIFICATION: [
    {
      key: 'SYSTEM_NOTIFY_TEMPLATE',
      name: 'Noti Template',
    },
    {
      key: 'SYSTEM_NOTIFY_ENABLE',
      name: 'Enable',
    },
  ],
  ADMIN_MAIL_SETTINGS: [
    {
      key: 'MAIL_NOTIFY_SERVER_SENDER',
      name: 'Sender',
    }, {
      key: 'MAIL_NOTIFY_SERVER_USERNAME',
      name: 'Username',
    },
    {
      key: 'MAIL_NOTIFY_SERVER_PASSWORD',
      name: 'Password',
    },
    {
      key: 'MAIL_NOTIFY_SERVER_HOST',
      name: 'Host',
    },
    {
      key: 'MAIL_NOTIFY_SERVER_PORT',
      name: 'Port',
    },
  ],
  MAIL_SETTINGS: [
    {
      key: 'EMAIL_SERVER_SENDER',
      name: 'Sender',
    }, {
      key: 'EMAIL_SERVER_USERNAME',
      name: 'Username',
    },
    {
      key: 'EMAIL_SERVER_PASSWORD',
      name: 'Password',
    },
    {
      key: 'EMAIL_SERVER_HOST',
      name: 'Host',
    },
    {
      key: 'EMAIL_SERVER_PORT',
      name: 'Port',
    },
    {
      key: 'EMAIL_NOTIFICATION_ADMIN',
      name: 'Admin Email',
    },
  ],
};
