import React from 'react';
import {injectIntl} from 'react-intl';
import Button from 'components/common/Button';
import ButtonLink from 'components/ButtonLink';
import ActionDialog from 'components/common/ActionDialog';
import {TO} from 'utils/utilHelper';
import messages from '../messages';
import styled from 'styled-components';
import {Form, Formik} from 'formik';
import getSchema from './validateSchema';
import {Col, Row} from 'reactstrap';
import get from 'lodash/get';
import {compose} from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import {updateCateConfig} from 'services/admin/configuration.service';
import FormInputGroup from 'components/common/FormInputGroup';
import {errorCode} from 'constants/responseCode';
import {configCategory} from '../utils';

const StyledContainer = styled(ActionDialog)`

`;


class AdvPopup extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initData: [],
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.selectedId !== '') {
      this.setState({
        initData: nextProps.selectedObject,
      });
    } else {
      const initData = [];
      this.setState({
        initData,
      });
    }
  }

  handleSubmit = async (values, { setSubmitting }) => {
    const dataSubmit = {
      ...values,
    };

    const { forceRefresh, handleOnClose, intl } = this.props;
    const [err, response] = await TO(updateCateConfig(dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.msgUpdateSuccess));
      handleOnClose();
      forceRefresh();
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    setSubmitting(false);
  }


  render() {
    const self = this;
    const {
      intl,
      isOpen,
      handleOnClose,
      selectedCategory,
    } = this.props;

    const {
      initData,
    } = self.state;

    return (
      <StyledContainer
        portalClassName="custom-portal"
        title={`Update ${selectedCategory}`}
        usePortal
        canOutsideClickClose
        canEscapeKeyClose
        isOpen={isOpen}
        onClose={handleOnClose}
        width={800}
      >
        <Wrapper className="m-4">
          <Formik
            onSubmit={this.handleSubmit}
            initialValues={initData}
            enableReinitialize
            validationSchema={getSchema(intl)}
            render={(props) => (
              <Form>
                <Row>
                  {
                  configCategory[selectedCategory].map((item) => (
                    <Col md={{ size: 6 }} className="mb-2">
                      <FormInputGroup
                        didCheckErrors={false}
                        label={item.name}
                        isAsterisk
                        name={item.key}
                        type={'text'}
                        onChange={(e) => {
                          props.handleChange(e);
                          props.setFieldTouched(item.key, true, true);
                        }}
                        value={get(props.values, item.key, '')}
                      />
                    </Col>
                 ))
                }
                </Row>

                <div className="d-flex flex-column align-items-center">
                  <Button
                    primary
                    type="submit"
                    className="min-width-300 mt-4"
                    loading={props.isSubmitting}
                  >{intl.formatMessage(messages.update)}</Button>
                  <ButtonLink
                    onClick={handleOnClose}
                    type={'button'}
                  >{intl.formatMessage(messages.close)}</ButtonLink>
                </div>
              </Form>
           )}
          />
        </Wrapper>
      </StyledContainer>
    );
  }
}


AdvPopup.propTypes = {
};

const Wrapper = styled.div`
  margin-bottom: 10px;

  .content {
    border: 1px solid ${(props) => props.theme.colors.gray300};
    display: flex;
    flex-wrap: wrap;

    .label {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.small12};
      opacity: 0.5;
    }

    .bold-text {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.normal};
      font-weight: ${(props) => props.theme.fontWeights.strong};
      opacity: 0.8;
    }

    .group {
      padding: 10px 18px;
      background-color: ${(props) => props.theme.colors.white};
      width: 50%;

      &.gray {
        background-color: ${(props) => props.theme.colors.gray};
      }
    }
  }
`;


export default compose(
  WithHandleAlert,
  injectIntl
)(AdvPopup);
