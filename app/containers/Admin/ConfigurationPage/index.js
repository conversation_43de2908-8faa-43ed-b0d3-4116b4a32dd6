import React, { Fragment } from 'react';
import StyledContainer from 'containers/Admin/ConfigurationPage/styles';
import Card from 'components/Card';
import ConfigurationList from './ConfigurationList';
import FormInputGroup from 'components/common/FormInputGroup';
import { forwardTo } from '../../../utils/history';
import ButtonCreate from 'components/common/ButtonCreate';
import { routes } from 'containers/Routes/routeHelper';
import { injectIntl } from 'react-intl';
import { compose } from 'redux';
import messages from './messages';
import FilterDropdownGroupWrapper from 'components/FilterDropdownWrapper';
import { TO, convertDropdownList } from 'utils/utilHelper';
import DropdownList from 'components/DropdownList';
import ConfirmDialog from 'components/common/ConfirmDialog';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import auth from 'utils/auth';
import ConfigurationPopup from './ConfigurationPopup';
import AdvPopup from './AdvPopup';
import { getConfigurations } from 'services/admin/configuration.service';
import { configCategory } from './utils';

export class ConfigurationPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      dataList: [],
      isOpenAdvancedSearch: false,
      selectedId: props.match.params.id,
      forceRefresh: false,
      filteredList: {
        name: '',
        category: '',
      },
      isConfirm: false,
      categoryList: [],
    };
  }

  componentWillReceiveProps(nextProps) {
  }

  componentWillMount() {
    this.loadInitData();
  }

  loadInitData = async () => {
    const configs = await getConfigurations({
      filtered: [],
      pageSize: 100,
      page: 0,
    });

    const categoryList = [...new Map(configs.data.data.map((item) => [item.category, item])).values()];
    this.setState({
      categoryList: categoryList.map((el) => ({
        id: el.category,
        name: el.category,
      })),
    });
  }

  getKeyFromFilteredList = () => {
    const { filteredList, forceRefresh } = this.state;
    return `${filteredList.name}
            -${filteredList.category}
            -${forceRefresh}`;
  }

  handleUpdateModemList = (data) => {
    this.setState({
      dataList: data,
    });
  }

  handleSelectRow = (id) => {
    this.togglePopup(true, id);
  }


  forceRefresh = () => {
    this.setState({
      forceRefresh: !this.state.forceRefresh,
    });
  }

  handleSearch = (name, value) => {
    this.setState((prevState) => ({
      filteredList: {
        ...prevState.filteredList,
        [name]: value,
      },
    }));
  }

  handleMultiSearch = (payload) => {
    this.setState((prevState) => ({
      filteredList: {
        ...prevState.filteredList,
        ...payload,
      },
    }));
  }

  handleClearAllFilters = () => {
    this.setState({
      filteredList: {
        name: '',
        category: '',
      },
    });
  }

  handleAdvancedSearch = (isOpen = true) => {
    this.setState({
      isOpenAdvancedSearch: isOpen,
    });
  }

  handleRemoveRow = (id) => {
    this.setState({
      selectedId: id,
      isConfirm: true,
    });
  }

  handleCloseConfirmPopup = () => {
    this.setState({
      isConfirm: false,
    });
  }

  togglePopup = async (isOpen = true, category) => {
    if (isOpen) {
      const response = await getConfigurations({
        filtered: [{
          id: 'category',
          value: category,
        }],
        pageSize: 100,
        page: 0,
      });


      const selectedObject = {};
      const data = response.data.data;

      const keys = configCategory[category];
      keys.map((item) => {
        selectedObject[item.key] = data.filter((el) => el.key === item.key)[0].value;
        return item;
      });

      this.setState({
        isOpenPopupStripe: true,
        selectedCategory: category,
        selectedObject,
      });
    } else {
      this.setState({
        isOpenPopup: false,
        isOpenPopupStripe: false,
      });
    }
  }

  render() {
    const { intl } = this.props;
    const { filteredList, dataList, selectedId, selectedObject, isOpenPopup, isOpenPopupStripe, isConfirm, categoryList, selectedCategory } = this.state;
    const cateFilter = convertDropdownList(categoryList, 'All', '');

    return (
      <StyledContainer>
        <Fragment>
          <Card>
            <div className="margin-bottom-13 d-flex justify-content-between">
              <FilterDropdownGroupWrapper>
                <div className="row no-gutters min-width-100">
                  <div className={'col-md-2'}>
                    <DropdownList
                      label={'Group'}
                      value={cateFilter.find((option) =>
                        option.value === filteredList.category
                      )}
                      options={cateFilter}
                      onChange={(option) => {
                        this.handleSearch('category', option.value);
                      }}
                    />
                  </div>
                  <div className={'col-md-2'}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={intl.formatMessage(messages.search)}
                      name="name"
                      onChange={(e) => {
                        this.handleSearch('name', e.target.value);
                      }}
                      type={'text'}
                      value={filteredList.name}
                      placeholder={'Key'}
                    />
                  </div>
                </div>
              </FilterDropdownGroupWrapper>
            </div>

            <ConfigurationList
              dataList={dataList}
              filteredList={filteredList}
              handleUpdateModemList={this.handleUpdateModemList}
              getKeyFromFilteredList={this.getKeyFromFilteredList}
              handleSelectRow={this.handleSelectRow}
              forceRefresh={this.forceRefresh}
            />
          </Card>
        </Fragment>
        <ConfirmDialog
          message={intl.formatMessage(messages.messageConfirm)}
          title={intl.formatMessage(messages.titleConfirm)}
          isOpen={isConfirm}
          confirmButtonText={intl.formatMessage(messages.confirmButton)}
          cancelButtonText={intl.formatMessage(messages.cancelButton)}
          onConfirm={this.handleAcceptConfirmPopup}
          onClose={this.handleCloseConfirmPopup}
          focusCloseButton
        />
        <ConfigurationPopup
          selectedId={selectedId}
          selectedObject={selectedObject}
          isOpen={isOpenPopup}
          handleOnClose={() => this.togglePopup(false)}
          forceRefresh={this.forceRefresh}
        />
        <AdvPopup
          selectedId={selectedId}
          selectedObject={selectedObject}
          isOpen={isOpenPopupStripe}
          handleOnClose={() => this.togglePopup(false)}
          forceRefresh={this.forceRefresh}
          selectedCategory={selectedCategory}
        />
      </StyledContainer>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(ConfigurationPage);
