import React, { Fragment } from 'react';
import { injectIntl } from 'react-intl';
import Button from 'components/common/Button';
import ButtonLink from 'components/ButtonLink';
import ActionDialog from 'components/common/ActionDialog';
import { TO, convertDropdownList } from 'utils/utilHelper';
import messages from '../messages';
import styled from 'styled-components';
import { Formik, Form } from 'formik';
import getSchema from './validateSchema';
import { Row, Col } from 'reactstrap';
import get from 'lodash/get';
import { compose } from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import { updateConfiguration, sendNotifyEmail } from 'services/admin/configuration.service';
import FormInputGroup from 'components/common/FormInputGroup';
import moment from 'moment';
import { errorCode } from 'constants/responseCode';
import CKEditor from 'ckeditor4-react';
import FormLabel from 'components/common/FormLabel';
import FormCheckBox from 'components/common/FormCheckBox';

const StyledContainer = styled(ActionDialog)`

`;

const NOTIFY_CONFIG_TEMPLATES = ['EMAIL_NOTIFY_TEMPLATE', 'SYSTEM_NOTIFY_TEMPLATE'];
const EMAIL_NOTIFY_TEMPLATES = ['EMAIL_NOTIFY_TEMPLATE'];

class ConfigurationPopup extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initData: {
        title: '',
        value: '',
        description: '',
      },
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.selectedId !== '') {
      this.setState({
        initData: nextProps.selectedObject,
      });
    } else {
      const initData = {
        title: '',
        value: '',
        description: '',
      };
      this.setState({
        initData,
      });
    }
  }

  handleSubmit = async (values, { setSubmitting }) => {
    const dataSubmit = {
      ...values,
    };

    const { selectedId, forceRefresh, handleOnClose, intl } = this.props;
    const [err, response] = await TO(updateConfiguration(selectedId, dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.msgUpdateSuccess));
      handleOnClose();
      forceRefresh();
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    setSubmitting(false);
  }

  handleSendMail = async (values) => {
    const { intl } = this.props;
    const dataSubmit = {
      ...values,
    };

    const [err, response] = await TO(sendNotifyEmail(dataSubmit));
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess('Send email success!');
      // handleOnClose();
      // forceRefresh();
    } else {
      this.props.handleAlertError('Send email error!');
    }
  }

  render() {
    const self = this;
    const {
      intl,
      isOpen,
      handleOnClose,
      selectedId,
    } = this.props;

    const {
      initData,
    } = self.state;


    return (
      <StyledContainer
        portalClassName="custom-portal"
        title={'Update'}
        usePortal
        canOutsideClickClose
        canEscapeKeyClose
        isOpen={isOpen}
        onClose={handleOnClose}
        width={600}
      >
        <Wrapper className="m-4">
          <Formik
            onSubmit={this.handleSubmit}
            initialValues={initData}
            enableReinitialize
            validationSchema={getSchema(intl)}
            render={(props) => (
              <Form>
                <Row>
                  <Col md={{ size: 12 }}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Key'}
                      isAsterisk
                      name="key"
                      type={'text'}
                      value={get(props.values, 'key', '')}
                      disabled
                    />
                  </Col>
                  {
                    EMAIL_NOTIFY_TEMPLATES.includes(get(props.values, 'key')) && <Col md={{ size: 12 }}>
                      <FormInputGroup
                        didCheckErrors={false}
                        label={'Title'}
                        name="title"
                        isAsterisk
                        onChange={(e) => {
                          props.handleChange(e);
                          props.setFieldTouched('title', true, true);
                        }}
                        type={'text'}
                        value={get(props.values, 'title', '')}
                      />
                    </Col>
                  }
                  <Col md={{ size: 12 }}>
                    {
                      NOTIFY_CONFIG_TEMPLATES.includes(get(props.values, 'key')) ?
                        <div className="mb-2">
                          <FormLabel isAsterisk>Body</FormLabel>
                          <CKEditor
                            data={get(props.values, 'value', '')}
                            onChange={(evt) => {
                              props.setFieldValue('value', evt.editor.getData());
                            }}
                            config={{
                              toolbar: [
                                ['Source'],
                                ['Styles', 'Format', 'Font', 'FontSize'],
                                ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript'],
                                ['Undo', 'Redo'],
                                ['EasyImageUpload'],
                                ['About'],
                              ],
                              extraPlugins: 'easyimage',
                              removePlugins: 'image',
                              autoParagraph: false,
                            }}
                          />
                        </div>
                        :
                        <FormInputGroup
                          didCheckErrors={false}
                          label={'Value'}
                          isAsterisk
                          name="value"
                          onChange={(e) => {
                            props.handleChange(e);
                            props.setFieldTouched('value', true, true);
                          }}
                          type={'text'}
                          value={get(props.values, 'value', '')}
                        />
                    }
                  </Col>
                </Row>
                <Row>
                  <Col md={{ size: 12 }}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Description'}
                      isAsterisk
                      name="description"
                      onChange={(e) => {
                        props.handleChange(e);
                        props.setFieldTouched('description', true, true);
                      }}
                      type={'text'}
                      value={get(props.values, 'description', '')}
                    />
                  </Col>
                </Row>


                {
                  EMAIL_NOTIFY_TEMPLATES.includes(get(props.values, 'key')) && <Fragment>
                    <Row>
                      <Col md={{ size: 12 }}>
                        <div className="mt-3 mb-3" style={{ borderBottom: 'double' }}></div>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={{ size: 12 }}>
                        <FormCheckBox
                          label={'Send all customers'}
                          name={'allCustomer'}
                          value={get(props.values, 'allCustomer')}
                          onChange={(e) => {
                            props.handleChange(e);
                          }}
                          {...props}
                        />
                      </Col>
                    </Row>
                    <Row>
                      <Col md={{ size: 12 }}>
                        <FormInputGroup
                          didCheckErrors={false}
                          label={'Emails'}
                          name="emails"
                          onChange={(e) => {
                            props.handleChange(e);
                          }}
                          type={'text'}
                          value={get(props.values, 'emails', '')}
                          isTips
                          tips={'Separate emails with semicolons (;)'}
                        />
                      </Col>
                    </Row>
                  </Fragment>
                }

                <div className="d-flex flex-column align-items-center">
                  <Button
                    primary
                    type="submit"
                    className="min-width-300 mt-4"
                    loading={props.isSubmitting}
                  >{intl.formatMessage(messages.update)}</Button>
                  {
                    EMAIL_NOTIFY_TEMPLATES.includes(get(props.values, 'key')) && <Button
                      blue
                      type="button"
                      className="min-width-300 mt-4"
                      onClick={() => this.handleSendMail(props.values)}
                    >Send mail</Button>
                  }
                  <ButtonLink
                    onClick={handleOnClose}
                    type={'button'}
                  >{intl.formatMessage(messages.close)}</ButtonLink>
                </div>
              </Form>
           )}
          />
        </Wrapper>
      </StyledContainer>
    );
  }
}


ConfigurationPopup.propTypes = {
};

const Wrapper = styled.div`
  margin-bottom: 10px;

  .content {
    border: 1px solid ${(props) => props.theme.colors.gray300};
    display: flex;
    flex-wrap: wrap;

    .label {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.small12};
      opacity: 0.5;
    }

    .bold-text {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.normal};
      font-weight: ${(props) => props.theme.fontWeights.strong};
      opacity: 0.8;
    }

    .group {
      padding: 10px 18px;
      background-color: ${(props) => props.theme.colors.white};
      width: 50%;

      &.gray {
        background-color: ${(props) => props.theme.colors.gray};
      }
    }
  }
`;


export default compose(
  WithHandleAlert,
  injectIntl
)(ConfigurationPopup);
