import React, { Fragment } from 'react';
import { injectIntl } from 'react-intl';
import messages from '../messages';
import { formatDataList } from 'utils/utilHelper';
import TransactionList from 'components/Transactions/TransactionList';
import { getMonitors } from 'services/admin/monitor.service';
import WithHandlePromise from 'containers/WithHandlePromise';
import { compose } from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import auth from 'utils/auth';
import moment from 'moment';
import { permission } from 'constants/permission';
import FormTextArea from 'components/common/FormTextArea';

const getColumns = ({
                      intl,
                    }) => (
  [
    {
      Header: 'No.',
      accessor: 'index',
      headerClassName: 'table-header',
      width: 50,
    },
    {
      Header: 'Type',
      accessor: 'code',
      headerClassName: 'table-header',
      width: 200,
    },
    {
      Header: 'Email',
      accessor: 'object',
      headerClassName: 'table-header',
      width: 200,
    },
    {
      Header: 'Notification',
      accessor: 'description',
      headerClassName: 'table-header',
      width: 500,
      Cell: (row) => (
        <FormTextArea
          rows={2}
          value={row.value}
        />),
    },
    {
      Header: 'Create Date',
      accessor: 'createdAt',
      headerClassName: 'table-header',
      Cell: (row) => moment(row.original.createdAt).format('DD/MM/YY HH:mm'),
    },
  ]
);

const DEFAULT_PAGES = -1;

class MonitorList extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      isLoading: false,
      pages: DEFAULT_PAGES,
    };
  }

  fetchData = async (state) => {
    this.setState({ isLoading: true });
    const { handleUpdateList } = this.props;
    const { page, pageSize, sorted } = state;
    const requestBody = {
      filtered: this.toFilteredList(),
      pageSize,
      page,
      sorted,
    };

    this.props.handlePromise(getMonitors(requestBody), (response) => {
      const { data, pages } = response.data;
      handleUpdateList(formatDataList(data, page, pageSize));
      this.setState({
        pages,
        isLoading: false,
      });
    });
  }

  toFilteredList = () => {
    const { filteredList } = this.props;
    return Object
      .entries(filteredList)
      .map((entry) => ({
        id: entry[0],
        value: Array.isArray(entry[1]) ? entry[1].join(',') : entry[1],
      }));
  };


  render() {
    const {
      intl,
      dataList,
      handleSelectRow,
      getKeyFromFilteredList,
    } = this.props;

    const { pages, isLoading } = this.state;
    const columns = getColumns({
      intl,
      handleSelectRow,
    });

    return (
      <Fragment>
        <TransactionList
          key={getKeyFromFilteredList()}
          manual
          data={dataList}
          pages={pages}
          loading={isLoading}
          columns={columns}
          onFetchData={this.fetchData}
          defaultSorted={[
            {
              id: 'createdDate',
              desc: true,
            },
          ]}
        />
      </Fragment>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(MonitorList);
