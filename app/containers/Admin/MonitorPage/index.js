import React, { Fragment } from 'react';
import StyledContainer from 'containers/Admin/MonitorPage/styles';
import Card from 'components/Card';
import MonitorList from './MonitorList';
import FormInputGroup from 'components/common/FormInputGroup';
import { forwardTo } from '../../../utils/history';
import ButtonCreate from 'components/common/ButtonCreate';
import { routes } from 'containers/Routes/routeHelper';
import { injectIntl } from 'react-intl';
import { compose } from 'redux';
import messages from './messages';
import FilterDropdownGroupWrapper from 'components/FilterDropdownWrapper';
import { TO, convertDropdownList } from 'utils/utilHelper';
import DropdownList from 'components/DropdownList';
import ConfirmDialog from 'components/common/ConfirmDialog';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import auth from 'utils/auth';
import FormInputDatePicker from 'components/common/FormInputDatePicker';

export class MonitorPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      dataList: [],
      isOpenAdvancedSearch: false,
      forceRefresh: false,
      filteredList: {
        type: '',
        email: '',
        createdDate: null,
        customerId: this.props.customerId ? this.props.customerId : '',
      },
    };
  }

  componentWillReceiveProps(nextProps) {
  }

  componentWillMount() {
    this.loadInitData();
  }

  loadInitData = async () => {

  }

  getKeyFromFilteredList = () => {
    const { filteredList, forceRefresh } = this.state;
    return `${filteredList.type}
            -${filteredList.email}
            -${filteredList.createdDate}
            -${forceRefresh}`;
  }

  handleUpdateList = (data) => {
    this.setState({
      dataList: data,
    });
  }


  forceRefresh = () => {
    this.setState({
      forceRefresh: !this.state.forceRefresh,
    });
  }

  handleSearch = (name, value) => {
    this.setState((prevState) => ({
      filteredList: {
        ...prevState.filteredList,
        [name]: value,
      },
    }));
  }

  handleMultiSearch = (payload) => {
    this.setState((prevState) => ({
      filteredList: {
        ...prevState.filteredList,
        ...payload,
      },
    }));
  }

  handleClearAllFilters = () => {
    this.setState({
      filteredList: {
        type: '',
        email: '',
        createdDate: null,
        customerId: this.props.customerId ? this.props.customerId : '',
      },
    });
  }

  handleAdvancedSearch = (isOpen = true) => {
    this.setState({
      isOpenAdvancedSearch: isOpen,
    });
  }

  handleRemoveRow = (id) => {
    this.setState({
      isConfirm: true,
    });
  }

  handleCloseConfirmPopup = () => {
    this.setState({
      isConfirm: false,
    });
  }

  render() {
    const { intl } = this.props;
    const { filteredList, dataList, isConfirm } = this.state;
    const notiTypeList = [{ id: '', name: 'All' }, { id: 'SIGNUP', name: 'SIGNUP' }, { id: 'TOPUP', name: 'TOPUP' }, { id: 'ALERT_PROXY', name: 'ALERT PROXY' }];

    return (
      <StyledContainer>
        <Fragment>
          <Card>
            <div className="margin-bottom-13 d-flex justify-content-between">
              <FilterDropdownGroupWrapper>
                <div className="row no-gutters min-width-100">
                  <div className={'col-md-2'}>
                    <DropdownList
                      label={'Type'}
                      value={convertDropdownList(notiTypeList).find((option) =>
                        option.value === filteredList.type
                      )}
                      options={convertDropdownList(notiTypeList)}
                      onChange={(option) => {
                        this.handleSearch('type', option.value);
                      }}
                    />
                  </div>
                  <div className={'col-md-2'}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Email'}
                      name="email"
                      onChange={(e) => {
                        this.handleSearch('email', e.target.value);
                      }}
                      type={'text'}
                      value={filteredList.name}
                    />
                  </div>
                  <div className={'col-md-2'}>
                    <FormInputDatePicker
                      name="createdDate"
                      value={filteredList.createdDate}
                      label={'Date'}
                      handleSearch={(name, value) => {
                        this.handleSearch(name, value);
                      }}
                    />
                  </div>
                </div>
              </FilterDropdownGroupWrapper>
            </div>

            <MonitorList
              dataList={dataList}
              filteredList={filteredList}
              handleUpdateList={this.handleUpdateList}
              getKeyFromFilteredList={this.getKeyFromFilteredList}
              forceRefresh={this.forceRefresh}
            />
          </Card>
        </Fragment>
        <ConfirmDialog
          message={intl.formatMessage(messages.messageConfirm)}
          title={intl.formatMessage(messages.titleConfirm)}
          isOpen={isConfirm}
          confirmButtonText={intl.formatMessage(messages.confirmButton)}
          cancelButtonText={intl.formatMessage(messages.cancelButton)}
          onConfirm={this.handleAcceptConfirmPopup}
          onClose={this.handleCloseConfirmPopup}
          focusCloseButton
        />
      </StyledContainer>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(MonitorPage);
