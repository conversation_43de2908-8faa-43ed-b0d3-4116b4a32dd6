import React, { Fragment } from 'react';
import StyledContainer from 'containers/Admin/ModemPage/styles';
import Card from 'components/Card';
import CustomerList from './CustomerList';
import FormInputGroup from 'components/common/FormInputGroup';
import { forwardTo } from '../../../utils/history';
import { routes } from 'containers/Routes/routeHelper';
import { injectIntl } from 'react-intl';
import { compose } from 'redux';
import messages from './messages';
import FilterDropdownGroupWrapper from 'components/FilterDropdownWrapper';
import { TO } from 'utils/utilHelper';
import AdvancedSearchPopup from './AdvancedSearchPopup';
import ConfirmDialog from 'components/common/ConfirmDialog';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import ChangePasswordPopup from './ChangePasswordPopup';
import TopupPopup from './TopupPopup';
import RefundPopup from './RefundPopup';
import { errorCode } from 'constants/responseCode';
import { suspendedAccount, activeAccount } from 'services/admin/customer.service';
import Button from 'components/common/Button';
import env from 'env';

export class CustomerPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      dataList: [],
      isOpenAdvancedSearch: false,
      selectedId: null,
      forceRefresh: false,
      filteredList: {
        name: '',
        email: '',
      },
      isConfirm: false,
      isOpenChangePassword: false,
    };
  }

  componentWillReceiveProps(nextProps) {
  }

  componentWillMount() {
    this.loadInitData();
  }

  loadInitData = async () => {

  }

  getKeyFromFilteredList = () => {
    const { filteredList, forceRefresh } = this.state;
    return `${filteredList.name}
            -${filteredList.email}
            -${forceRefresh}`;
  }

  handleUpdateModemList = (data) => {
    this.setState({
      dataList: data,
    });
  }

  handleSelectRow = (id) => {
    this.setState({
      isOpenChangePassword: true,
      selectedId: id,
    });
  };

  handleCloseDetails = () => () => {
    forwardTo(routes.ADMIN_MODEM);
  };

  forceRefresh = () => {
    this.setState({
      forceRefresh: !this.state.forceRefresh,
    });
  }

  handleSearch = (name, value) => {
    this.setState((prevState) => ({
      filteredList: {
        ...prevState.filteredList,
        [name]: value,
      },
    }));
  }

  handleMultiSearch = (payload) => {
    this.setState((prevState) => ({
      filteredList: {
        ...prevState.filteredList,
        ...payload,
      },
    }));
  }

  handleClearAllFilters = () => {
    this.setState({
      filteredList: {
        name: '',
        email: '',
      },
    });
  }

  handleAdvancedSearch = (isOpen = true) => {
    this.setState({
      isOpenAdvancedSearch: isOpen,
    });
  }

  handleRemoveRow = (id) => {
    this.setState({
      selectedId: id,
      isConfirm: true,
    });
  }

  handleCloseConfirmPopup = () => {
    this.setState({
      isConfirm: false,
    });
  }

  handleAcceptConfirmPopup = async () => {
    const { intl } = this.props;
    const { selectedId, confirmType } = this.state;
    this.setState({
      btnLoading: true,
    });

    const [err, response] = await TO(confirmType === 'ACTIVE' ? activeAccount(selectedId) : suspendedAccount(selectedId));
    if (err) {
      console.log(err);
    }

    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.msgUpdateSuccess));
      this.forceRefresh();
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    this.setState({
      isConfirm: false,
      btnLoading: false,
    });
  }

  togglePopup = (isOpen = true) => {
    this.setState({
      isOpenChangePassword: isOpen,
    });
    if (!isOpen) {
      this.setState({
        selectedId: null,
      });
    }
  }

  handleTopup = (isOpen = true, id = null) => {
    this.setState({
      isOpenTopup: isOpen,
      selectedId: id,
    });
    if (!isOpen) {
      this.setState({
        selectedId: null,
      });
    }
  }

  handleRefund = (isOpen = true, id = null) => {
    this.setState({
      isOpenRefund: isOpen,
      selectedId: id,
    });
    if (!isOpen) {
      this.setState({
        selectedId: null,
      });
    }
  }

  handleSuspended = (id) => {
    this.setState({
      selectedId: id,
      confirmType: 'SUSPENDED',
      isConfirm: true,
    });
  }

  handleActive = (id) => {
    this.setState({
      selectedId: id,
      confirmType: 'ACTIVE',
      isConfirm: true,
    });
  }

  handleExportExcel = () => {
    window.open(`${env.API_URL}/admin/customers/excel`, '_blank');
  }

  render() {
    const { intl } = this.props;
    const { filteredList,
      dataList,
      isOpenAdvancedSearch,
      isConfirm,
      isOpenChangePassword,
      isOpenTopup,
      isOpenRefund,
      selectedId,
      btnLoading,
      confirmType,
    } = this.state;

    return (
      <StyledContainer>
        <Fragment>
          <Card>
            <div className="margin-bottom-13 d-flex justify-content-between">
              <FilterDropdownGroupWrapper>
                <div className="row no-gutters min-width-100">
                  <div className={'col-md-2'}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={intl.formatMessage(messages.search)}
                      name="name"
                      onChange={(e) => {
                        this.handleSearch('name', e.target.value);
                      }}
                      type={'text'}
                      value={filteredList.name}
                      placeholder={'Name'}
                    />
                  </div>
                  <div className={'col-md-2'}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Email'}
                      name="name"
                      onChange={(e) => {
                        this.handleSearch('email', e.target.value);
                      }}
                      type={'text'}
                      value={filteredList.email}
                      placeholder={'Email'}
                    />
                  </div>
                </div>
                <div className="margin-bottom-13 d-flex justify-content-between">
                  <div className="mt-2">
                    <Button
                      primary
                      small
                      onClick={() => this.handleExportExcel()}
                      type="button"
                      style={{ fontSize: 10, marginLeft: 5 }}
                    ><i className="fa fa-file-excel" /> Export file</Button>
                  </div>
                </div>
              </FilterDropdownGroupWrapper>
            </div>
            <CustomerList
              dataList={dataList}
              filteredList={filteredList}
              handleUpdateModemList={this.handleUpdateModemList}
              getKeyFromFilteredList={this.getKeyFromFilteredList}
              handleSelectRow={this.handleSelectRow}
              handleRemoveRow={this.handleRemoveRow}
              forceRefresh={this.forceRefresh}
              handleTopup={this.handleTopup}
              handleRefund={this.handleRefund}
              handleSuspended={this.handleSuspended}
              handleActive={this.handleActive}
            />
          </Card>
          <AdvancedSearchPopup
            isOpen={isOpenAdvancedSearch}
            handleOnClose={() => this.handleAdvancedSearch(false)}
            filteredList={filteredList}
            handleMultiSearch={this.handleMultiSearch}
          />
        </Fragment>
        <ConfirmDialog
          message={intl.formatMessage(confirmType === 'ACTIVE' ? messages.messageActive : messages.messageSuspended)}
          title={intl.formatMessage(messages.titleConfirm)}
          isOpen={isConfirm}
          confirmButtonText={intl.formatMessage(messages.confirmButton)}
          cancelButtonText={intl.formatMessage(messages.cancelButton)}
          onConfirm={this.handleAcceptConfirmPopup}
          onClose={this.handleCloseConfirmPopup}
          focusCloseButton
          loading={btnLoading}
        />
        <ChangePasswordPopup
          isOpen={isOpenChangePassword}
          selectedId={selectedId}
          handleOnClose={() => this.togglePopup(false)}
          forceRefresh={this.forceRefresh}
        />
        <TopupPopup
          isOpen={isOpenTopup}
          selectedId={selectedId}
          handleOnClose={() => this.handleTopup(false)}
          forceRefresh={this.forceRefresh}
        />
        <RefundPopup
          isOpen={isOpenRefund}
          selectedId={selectedId}
          handleOnClose={() => this.handleRefund(false)}
          forceRefresh={this.forceRefresh}
        />
      </StyledContainer>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(CustomerPage);
