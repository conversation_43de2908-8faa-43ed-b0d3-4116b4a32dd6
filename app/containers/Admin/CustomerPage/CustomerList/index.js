import React, { Fragment } from 'react';
import { injectIntl } from 'react-intl';
import messages from '../messages';
import { formatDataList } from 'utils/utilHelper';
import TransactionList from 'components/Transactions/TransactionList';
import { getCustomers } from 'services/admin/customer.service';
import WithHandlePromise from 'containers/WithHandlePromise';
import { compose } from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import auth from 'utils/auth';
import { formatCurrency } from '../../../../utils/numberHelper';
import { permission } from 'constants/permission';
import get from 'lodash/get';
import moment from 'moment';
import { forwardTo } from '../../../../utils/history';

const getColumns = ({
                      intl,
                      handleSelectRow,
                      handleTopup,
                      handleRefund,
                      handleSuspended,
                      handleActive,
                      handleViewDetail,
                    }) => (
  [
    {
      Header: intl.formatMessage(messages.no),
      accessor: 'index',
      headerClassName: 'table-header',
      width: 50,
    },
    {
      Header: 'Customer Code',
      accessor: 'uuid',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => (
        <a style={{ color: 'blue' }} onClick={() => handleViewDetail(row.original.uuid)}><span style={{ fontSize: 11 }}> {row.value}</span></a>
      ),
      width: 270,
    },
    {
      Header: 'Name',
      accessor: 'name',
      headerClassName: 'table-header',
      sortable: false,
    },
    {
      Header: 'Email',
      accessor: 'email',
      headerClassName: 'table-header',
      sortable: false,
    },
    {
      Header: 'Phone',
      accessor: 'phoneNumber',
      headerClassName: 'table-header',
      sortable: false,
    },
    {
      Header: 'Balance',
      accessor: 'balance',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => (
        <span>{formatCurrency(row.value)}</span>
      ),
    },
    {
      Header: 'Status',
      accessor: 'status',
      headerClassName: 'table-header',
      sortable: false,
    },
    {
      Header: 'Created',
      accessor: 'createdAt',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => (
        <span>{moment(row.value).format('MM/DD/YYYY')}</span>
      ),
    },
    {
      Header: intl.formatMessage(messages.action),
      accessor: 'id',
      className: 'action-cell',
      sortable: false,
      width: 120,
      Cell: (row) => (
        <Fragment>
          <a onClick={() => handleSelectRow(row.original.uuid)} className="ml-2 color-edit" title="Reset password"><i className="fa fa-user-lock" /></a>
          <a onClick={() => handleTopup(true, row.original.uuid)} className="ml-2 color-edit" title="Credit"><i className="fa fa-money-bill" /></a>
          <a onClick={() => handleRefund(true, row.original.uuid)} className="ml-2 color-edit" title="Debit"><i className="fa fa-money-check" /></a>
          <a onClick={() => handleSuspended(row.original.uuid)} className="ml-2 color-edit" title="Suspended"><i className="fa fa-lock" /></a>
          <a onClick={() => handleActive(row.original.uuid)} className="ml-2 color-edit" title="Active"><i className="fa fa-unlock" /></a>
        </Fragment>
      ),
      headerStyle: { justifyContent: 'center' },
      style: { justifyContent: 'center' },
    },
  ]
);

const DEFAULT_PAGES = -1;

class CustomerList extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      isLoading: false,
      pages: DEFAULT_PAGES,
    };
  }

  fetchData = async (state) => {
    this.setState({ isLoading: true });
    const { handleUpdateModemList } = this.props;
    const { page, pageSize, sorted } = state;
    const requestBody = {
      filtered: this.toFilteredList(),
      pageSize,
      page,
      sorted,
    };

    this.props.handlePromise(getCustomers(requestBody), (response) => {
      const { data, pages } = response.data;
      handleUpdateModemList(formatDataList(data, page, pageSize));
      this.setState({
        pages,
        isLoading: false,
      });
    });
  }

  toFilteredList = () => {
    const { filteredList } = this.props;
    return Object
      .entries(filteredList)
      .map((entry) => ({
        id: entry[0],
        value: Array.isArray(entry[1]) ? entry[1].join(',') : entry[1],
      }));
  }

  handleViewDetail = (id) => {
    forwardTo(`/admin/customer-detail/${id}`);
  };

  render() {
    const {
      intl,
      dataList,
      getKeyFromFilteredList,
      handleSelectRow,
      handleTopup,
      handleRefund,
      handleSuspended,
      handleActive,
    } = this.props;

    const { pages, isLoading } = this.state;
    const columns = getColumns({
      intl,
      handleSelectRow,
      handleTopup,
      handleRefund,
      handleSuspended,
      handleActive,
      handleViewDetail: this.handleViewDetail,
    });

    return (
      <Fragment>
        <TransactionList
          key={getKeyFromFilteredList()}
          manual
          data={dataList}
          pages={pages}
          loading={isLoading}
          columns={columns}
          onFetchData={this.fetchData}
          defaultSorted={[
            {
              id: 'createdDate',
              desc: true,
            },
          ]}
        />
      </Fragment>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(CustomerList);
