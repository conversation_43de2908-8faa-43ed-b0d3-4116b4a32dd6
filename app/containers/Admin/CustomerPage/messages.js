import { defineMessages } from 'react-intl';

const scope = 'app.components.common';

export default defineMessages({
  search: {
    id: `${scope}.search`,
    defaultMessage: 'Search',
  },
  allLabel: {
    id: `${scope}.allLabel`,
    defaultMessage: 'All',
  },
  searchPlaceHolder: {
    id: `${scope}.searchPlaceHolder`,
    defaultMessage: 'Code, Name',
  },
  no: {
    id: `${scope}.no`,
    defaultMessage: 'NO',
  },
  status: {
    id: `${scope}.status`,
    defaultMessage: 'Status',
  },
  action: {
    id: `${scope}.action`,
    defaultMessage: 'Action',
  },
  update: {
    id: `${scope}.update`,
    defaultMessage: 'Update',
  },
  create: {
    id: `${scope}.create`,
    defaultMessage: 'Create',
  },
  close: {
    id: `${scope}.close`,
    defaultMessage: 'Close',
  },
  requiredNotNull: {
    id: `${scope}.requiredNotNull`,
    defaultMessage: 'This field is required',
  },
  msgUpdateSuccess: {
    id: `${scope}.msgUpdateSuccess`,
    defaultMessage: 'Update successful',
  },
  msgUpdateFailed: {
    id: `${scope}.msgUpdateFailed`,
    defaultMessage: 'Update failed',
  },
  msgCreateSuccess: {
    id: `${scope}.msgCreateSuccess`,
    defaultMessage: 'Create new successful',
  },
  msgCreateFailed: {
    id: `${scope}.msgCreateFailed`,
    defaultMessage: 'Create new failed',
  },
  msgDeleteSuccess: {
    id: `${scope}.msgDeleteSuccess`,
    defaultMessage: 'Delete successful',
  },
  msgDeleteFailed: {
    id: `${scope}.msgDeleteFailed`,
    defaultMessage: 'Delete failed',
  },
  notFound: {
    id: `${scope}.notFound`,
    defaultMessage: 'Data not found',
  },
  clearAllFiltersButton: {
    id: `${scope}.clearAllFiltersButton`,
    defaultMessage: 'Clear all filter',
  },
  advancedSearchButton: {
    id: `${scope}.advancedSearchButton`,
    defaultMessage: 'Advanced Search',
  },
  titleConfirm: {
    id: `${scope}.titleConfirm`,
    defaultMessage: 'Confirmation',
  },
  messageSuspended: {
    id: `${scope}.messageSuspended`,
    defaultMessage: 'Please review, this action will suspended this user and disable all license of customer?',
  },
  messageActive: {
    id: `${scope}.messageActive`,
    defaultMessage: 'Please review, this action will active this user?',
  },
  confirmButton: {
    id: `${scope}.confirmButton`,
    defaultMessage: 'Confirm',
  },
  cancelButton: {
    id: `${scope}.cancelButton`,
    defaultMessage: 'Cancel',
  },
  title: {
    id: `${scope}.title`,
    defaultMessage: 'Reset password',
  },

  subTitle: {
    id: `${scope}.subTitle`,
    defaultMessage: 'To reset your password, please enter your phone number below',
  },

  currentPassword: {
    id: `${scope}.currentPassword`,
    defaultMessage: 'Current password',
  },

  newPassword: {
    id: `${scope}.newPassword`,
    defaultMessage: 'Enter new password',
  },

  confirmPassword: {
    id: `${scope}.confirmPassword`,
    defaultMessage: 'Confirm password',
  },

  messageSuccess: {
    id: `${scope}.messageSuccess`,
    defaultMessage: 'Your password has been changed successfully.',
  },

  messageInvalidNewPassword: {
    id: `${scope}.messageInvalidNewPassword`,
    defaultMessage: 'The new password must not be the same as the old password.',
  },
  currentPasswordRequiredError: {
    id: `${scope}.currentPasswordRequiredError`,
    defaultMessage: 'Please enter your current password.',
  },
  new_passwordRequiredError: {
    id: `${scope}.new_passwordRequiredError`,
    defaultMessage: 'Please enter a new password.',
  },
  new_passwordMaxLengthError: {
    id: `${scope}.new_passwordMaxLengthError`,
    defaultMessage: 'Please enter a new password that is less than 30 characters.',
  },
  new_passwordMinLengthError: {
    id: `${scope}.new_passwordMinLengthError`,
    defaultMessage: 'Please enter more than 8 characters.',
  },
  new_passwordStrongError: {
    id: `${scope}.new_passwordStrongError`,
    defaultMessage: 'Password must be between 8 and 15 characters, with at least 1 letter and number.',
  },
  confirm_passwordRequiredError: {
    id: `${scope}.confirm_passwordRequiredError`,
    defaultMessage: 'Please re-enter the password.',
  },
  passwordNotMatchError: {
    id: `${scope}.passwordNotMatchError`,
    defaultMessage: 'Authentication password does not match.',
  },
});
