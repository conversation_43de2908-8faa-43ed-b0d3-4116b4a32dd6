import React, { Fragment } from 'react';
import StyledContainer from 'containers/Admin/ModemPage/styles';
import { routes } from 'containers/Routes/routeHelper';
import { injectIntl } from 'react-intl';
import { compose } from 'redux';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import { Tab, Tabs as BP3Tabs } from '@blueprintjs/core';
import CommonTabs from 'components/common/Tabs';
import Card from 'components/Card';
import { getCustomer } from 'services/admin/customer.service';
import LicensePage from '../LicensePage/index';
import TransactionPage from '../TransactionPage/index';
import MonitorPage from '../MonitorPage/index';

export class CustomerDetailPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      userId: props.match.params.id,
      customer: {},
    };
  }

  componentWillReceiveProps(nextProps) {
  }

  componentWillMount() {
    this.loadInitData();
  }

  loadInitData = async () => {
    const customer = await getCustomer(this.state.userId);
    this.setState({
      customer: customer.data,
    });
  }


  render() {
    const { intl } = this.props;
    const {
      userId,
      customer,
    } = this.state;

    const tabs = [
      {
        id: 'license',
        title: 'License',
        panel: <LicensePage
          customerId={userId}
        />,
      },
      {
        id: 'transaction',
        title: 'Transaction',
        panel: <TransactionPage
          customerId={userId}
        />,
      },
      {
        id: 'notification',
        title: 'Notification',
        panel: <MonitorPage
          customerId={userId}
        />,
      },
    ];

    return (
      <StyledContainer>
        <Fragment>
          <Card>
            <section className="mt-3">
              <label>Customer:</label> <h6> {customer.name} - {customer.email}</h6>
            </section>

            <CommonTabs
              onChange={this.handleOnChange}
              selectedTabId={'0'}
            >
              {tabs.map((item) => (
                <Tab
                  className="tab"
                  id={item.id}
                  title={item.title}
                  panel={item.panel}
                />
            ))}
            </CommonTabs>
          </Card>
        </Fragment>
      </StyledContainer>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(CustomerDetailPage);
