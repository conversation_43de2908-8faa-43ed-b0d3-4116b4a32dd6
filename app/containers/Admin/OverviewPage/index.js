import React, { Fragment } from 'react';
import StyledContainer from 'containers/Admin/OverviewPage/styles';
import { routes } from 'containers/Routes/routeHelper';
import { injectIntl } from 'react-intl';
import { compose } from 'redux';
import { TO, convertDropdownList } from 'utils/utilHelper';
import { eModemStatus } from 'enums/EModemStatus';
import { eModemType } from 'enums/EModemType';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import { Row, Col } from 'reactstrap';
import ProxyPageIcon from 'images/sidebarIcon/ic_currency.svg';
import CustomerPageIcon from 'images/sidebarIcon/ic_customer.svg';
import ModemPageIcon from 'images/sidebarIcon/ic_fee.svg';
import LicensePageIcon from 'images/sidebarIcon/ic-developer-board.svg';
import { getOverview } from 'services/admin/overview.service';
import Item from './Item';
import env from 'env';

export class OverviewPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      overview: {},
      dataList: [],
    };
  }

  componentWillReceiveProps(nextProps) {}

  componentWillMount() {
    this.props.handlePromise(getOverview(), (response) => {
      const { data } = response;
      this.setState({
        overview: {
          ...data,
        },
      });
    });
  }

  handleUpdateList = (data) => {
    this.setState({
      dataList: data,
    });
  };

  render() {
    const { intl } = this.props;
    const { dataList, overview } = this.state;
    return (
      <StyledContainer>
        <Row>
          <Col md={{ size: 8 }} className="pt-3">
            <h5>Summary</h5>
            <Row>
              <Col md={{ size: 6 }}>
                <Item
                  label={'# Total active user'}
                  value={overview.totalCustomers}
                  icon={CustomerPageIcon}
                  css={'bg-danger'}
                />
              </Col>
              <Col md={{ size: 6 }}>
                <Item
                  label={'# Total user with active license'}
                  value={overview.totalCusHaveActiveLicenses}
                  icon={CustomerPageIcon}
                  css={'bg-danger'}
                />
              </Col>
            </Row>
          </Col>
        </Row>
        <Row className="pt-3">
          <Col md={{ size: 6 }} className="pt-3">
            <h5>Proxy Mobile</h5>
            <div>
              <Row>
                <Col md={{ size: 6 }}>
                  <Item
                    label={'# Modem'}
                    value={overview.mtotalModems}
                    icon={ModemPageIcon}
                    css={'bg-info'}
                  />
                </Col>
                <Col md={{ size: 6 }}>
                  <Item
                    label={'# Modem paused'}
                    value={overview.mtotalPendingModems}
                    icon={ModemPageIcon}
                    css={'bg-info'}
                  />
                </Col>
              </Row>

              <Row className="mt-4">
                <Col md={{ size: 6 }}>
                  <Item
                    label={'# Proxy'}
                    value={overview.mtotalProxies}
                    icon={ProxyPageIcon}
                    css={'bg-success'}
                  />
                </Col>
                <Col md={{ size: 6 }}>
                  <Item
                    label={'# Available proxy'}
                    value={overview.mtotalAvailableProxies}
                    icon={ProxyPageIcon}
                    css={'bg-success'}
                  />
                </Col>
              </Row>

              <Row className="mt-4">
                <Col md={{ size: 6 }}>
                  <Item
                    label={'# Active license'}
                    value={overview.mtotalActiveLicenses}
                    icon={LicensePageIcon}
                    css={'bg-warning'}
                  />
                </Col>
                <Col md={{ size: 6 }}>
                  <Item
                    label={'# Expired license today'}
                    value={overview.mtotalExpiredLicenses}
                    icon={LicensePageIcon}
                    css={'bg-warning'}
                  />
                </Col>
              </Row>
            </div>
          </Col>
          {env.PROXY_MANUAL ? (
            <Col md={{ size: 6 }} className="pt-3">
              <h5>Proxy Manual</h5>
              <div>
                <Row>
                  <Col md={{ size: 6 }}>
                    <Item
                      label={'# modem'}
                      value={overview.totalModems}
                      icon={ModemPageIcon}
                      css={'bg-info'}
                    />
                  </Col>
                  <Col md={{ size: 6 }}>
                    <Item
                      label={'# modem paused'}
                      value={overview.totalPendingModems}
                      icon={ModemPageIcon}
                      css={'bg-info'}
                    />
                  </Col>
                </Row>

                <Row className="mt-4">
                  <Col md={{ size: 6 }}>
                    <Item
                      label={'# proxy'}
                      value={overview.totalProxies}
                      icon={ProxyPageIcon}
                      css={'bg-success'}
                    />
                  </Col>
                  <Col md={{ size: 6 }}>
                    <Item
                      label={'# available proxy'}
                      value={overview.totalAvailableProxies}
                      icon={ProxyPageIcon}
                      css={'bg-success'}
                    />
                  </Col>
                </Row>

                <Row className="mt-4">
                  <Col md={{ size: 6 }}>
                    <Item
                      label={'# active license'}
                      value={overview.totalActiveLicenses}
                      icon={LicensePageIcon}
                      css={'bg-warning'}
                    />
                  </Col>
                  <Col md={{ size: 6 }}>
                    <Item
                      label={'# expired license today'}
                      value={overview.totalExpiredLicenses}
                      icon={LicensePageIcon}
                      css={'bg-warning'}
                    />
                  </Col>
                </Row>
              </div>
            </Col>
          ) : (
            ''
          )}
        </Row>
      </StyledContainer>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(OverviewPage);
