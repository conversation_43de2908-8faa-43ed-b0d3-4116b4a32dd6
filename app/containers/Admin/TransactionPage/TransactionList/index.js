import React, {Fragment} from 'react';
import {injectIntl} from 'react-intl';
import messages from '../messages';
import {formatDataList} from 'utils/utilHelper';
import TransactionList from 'components/Transactions/TransactionList';
import {getTransactions} from 'services/admin/transaction.service';
import WithHandlePromise from 'containers/WithHandlePromise';
import {compose} from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import auth from 'utils/auth';
import {permission} from 'constants/permission';
import {formatCurrency} from '../../../../utils/numberHelper';
import moment from 'moment';
import FormTextArea from 'components/common/FormTextArea';
import get from "lodash/get";

const getColumns = ({
                      intl,
                    }) => (
  [
    {
      Header: intl.formatMessage(messages.no),
      accessor: 'index',
      headerClassName: 'table-header',
      width: 50,
    },
    {
      Header: 'Code',
      accessor: 'uuid',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => (
        <span style={{fontSize: 11}}>{row.value}</span>
      ),
      width: 270,
    },
    {
      Header: 'Customer',
      accessor: 'customer.name',
      headerClassName: 'table-header',
      sortable: false,
    },
    {
      Header: 'Type',
      accessor: 'type',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => (
        <span>{row.value === 'REFUND' ? 'DEBIT' : (row.value === 'TOPUP' ? 'CREDIT' : row.value)}</span>
      )
    },
    {
      Header: 'Amount',
      accessor: 'amount',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => (
        <span>{formatCurrency(row.value)}</span>
      ),
    },
    {
      Header: 'Promotion Code',
      accessor: 'promoCode',
      headerClassName: 'table-header',
      sortable: false,
    },
    {
      Header: 'Discount Amount',
      accessor: 'discount',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => (
        <span>{formatCurrency(row.value)}</span>
      ),
    },
    {
      Header: 'Created Date',
      accessor: 'createdAt',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => (
        <span>{moment(row.value).format('MM/DD/YYYY')}</span>
      ),
    },
    {
      Header: intl.formatMessage(messages.status),
      accessor: 'status',
      headerClassName: 'table-header',
      sortable: false,
    },
    {
      Header: 'Description',
      accessor: 'description',
      headerClassName: 'table-header',
      sortable: false,
      width: 300,
      Cell: (row) => (
        <FormTextArea
          rows={2}
          value={row.value}
        />),
    },
    {
      Header: 'Payment Info',
      accessor: 'index',
      headerClassName: 'table-header',
      sortable: false,
      width: 300,
      Cell: (row) => {
        const val = row.original;
        const txt = 'Address: ' + val.payAddress
          + '\nAmount: ' + val.payAmount
          + '\nCurrency: ' + val.payCurrency;

        if (val.payAddress) {
          return (
            <FormTextArea
              rows={2}
              value={txt}
            />
          );
        }
        return '';
      }
    },
  ]
);

const DEFAULT_PAGES = -1;

class PackageList extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      isLoading: false,
      pages: DEFAULT_PAGES,
    };
  }

  fetchData = async (state) => {
    this.setState({isLoading: true});
    const {handleUpdateModemList} = this.props;
    const {page, pageSize, sorted} = state;
    const requestBody = {
      filtered: this.toFilteredList(),
      pageSize,
      page,
      sorted,
    };

    this.props.handlePromise(getTransactions(requestBody), (response) => {
      const {data, pages} = response.data;
      handleUpdateModemList(formatDataList(data, page, pageSize));
      this.setState({
        pages,
        isLoading: false,
      });
    });
  };

  toFilteredList = () => {
    const {filteredList} = this.props;
    return Object
      .entries(filteredList)
      .map((entry) => ({
        id: entry[0],
        value: Array.isArray(entry[1]) ? entry[1].join(',') : entry[1],
      }));
  };

  render() {
    const {
      intl,
      dataList,
      getKeyFromFilteredList,
    } = this.props;

    const {pages, isLoading} = this.state;
    const columns = getColumns({
      intl,
    });

    return (
      <Fragment>
        <TransactionList
          key={getKeyFromFilteredList()}
          manual
          data={dataList}
          pages={pages}
          loading={isLoading}
          columns={columns}
          onFetchData={this.fetchData}
          defaultSorted={[
            {
              id: 'createdDate',
              desc: true,
            },
          ]}
        />
      </Fragment>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl,
)(PackageList);
