import * as Yup from 'yup';
import messages from '../messages';

export default (intl) => (Yup.object().shape({
  name: Yup.string()
    .required(intl.formatMessage(messages.requiredNotNull)),
  title: Yup.string()
    .required(intl.formatMessage(messages.requiredNotNull)),
  body: Yup.string()
    .required(intl.formatMessage(messages.requiredNotNull)),
  description: Yup.string()
    .required(intl.formatMessage(messages.requiredNotNull)),
}));
