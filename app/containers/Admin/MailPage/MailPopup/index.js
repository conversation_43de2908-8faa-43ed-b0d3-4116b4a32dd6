import React, { Fragment } from 'react';
import { injectIntl } from 'react-intl';
import Button from 'components/common/Button';
import ButtonLink from 'components/ButtonLink';
import ActionDialog from 'components/common/ActionDialog';
import { TO, convertDropdownList } from 'utils/utilHelper';
import messages from '../messages';
import styled from 'styled-components';
import { Formik, Form } from 'formik';
import getSchema from './validateSchema';
import { Row, Col } from 'reactstrap';
import get from 'lodash/get';
import { compose } from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import { updateMailTemplate, insertMailTemplate, sendNotifyEmail } from 'services/admin/mail.service';
import FormInputGroup from 'components/common/FormInputGroup';
import moment from 'moment';
import { errorCode } from 'constants/responseCode';
import CKEditor from 'ckeditor4-react';
import FormLabel from 'components/common/FormLabel';
import FormCheckBox from 'components/common/FormCheckBox';

const StyledContainer = styled(ActionDialog)`
`;


class ConfigurationPopup extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initData: {
        name: '',
        title: '',
        body: '',
        description: '',
      },
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.selectedId !== '') {
      this.setState({
        initData: nextProps.selectedObject,
      });
    } else {
      const initData = {
        name: '',
        title: '',
        body: '',
        description: '',
      };
      this.setState({
        initData,
      });
    }
  }

  handleSubmit = async (values, { setSubmitting }) => {
    const dataSubmit = {
      ...values,
    };

    if (this.props.selectedId !== '') {
      this.handleSubmitUpdate(dataSubmit, setSubmitting);
    } else {
      this.handleSubmitCreate(dataSubmit, setSubmitting);
    }
  }

  handleSubmitUpdate = async (dataSubmit, setSubmitting) => {
    const { selectedId, forceRefresh, handleOnClose, intl } = this.props;
    const [err, response] = await TO(updateMailTemplate(selectedId, dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.msgUpdateSuccess));
      handleOnClose();
      forceRefresh();
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    setSubmitting(false);
  }

  handleSubmitCreate = async (dataSubmit, setSubmitting) => {
    const { forceRefresh, handleOnClose, intl } = this.props;
    const [err, response] = await TO(insertMailTemplate(dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.msgCreateSuccess));
      handleOnClose();
      forceRefresh();
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgCreateFailed));
    }
    setSubmitting(false);
  }


  handleSendMail = async (values) => {
    const { forceRefresh, handleOnClose, intl } = this.props;
    const dataSubmit = {
      ...values,
    };

    const [err, response] = await TO(sendNotifyEmail(dataSubmit));
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess('Send email success!');
      handleOnClose();
      forceRefresh();
    } else {
      this.props.handleAlertError('Send email error!');
    }
  }

  render() {
    const self = this;
    const {
      intl,
      isOpen,
      handleOnClose,
      selectedId,
      isSendMail,
    } = this.props;

    const {
      initData,
    } = self.state;


    return (
      <StyledContainer
        portalClassName="custom-portal"
        title={isSendMail ? 'Send mail' :
          (selectedId !== '' ? intl.formatMessage(messages.editLabel)
          : intl.formatMessage(messages.addLabel))
        }
        usePortal
        canOutsideClickClose
        canEscapeKeyClose
        isOpen={isOpen}
        onClose={handleOnClose}
        width={600}
      >
        <Wrapper className="m-4">
          <Formik
            onSubmit={this.handleSubmit}
            initialValues={initData}
            enableReinitialize
            validationSchema={getSchema(intl)}
            render={(props) => (
              <Form>
                <Row>
                  <Col md={{ size: 12 }}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Name'}
                      isAsterisk
                      onChange={(e) => {
                        props.handleChange(e);
                        props.setFieldTouched('name', true, true);
                      }}
                      name="name"
                      type={'text'}
                      value={get(props.values, 'name', '')}
                      disabled={selectedId !== ''}
                    />
                  </Col>
                  <Col md={{ size: 12 }}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Title'}
                      name="title"
                      isAsterisk
                      onChange={(e) => {
                        props.handleChange(e);
                        props.setFieldTouched('title', true, true);
                      }}
                      type={'text'}
                      value={get(props.values, 'title', '')}
                    />
                  </Col>

                  <Col md={{ size: 12 }}>
                    <div className="mb-2">
                      <FormLabel isAsterisk>Body</FormLabel>
                      <CKEditor
                        data={get(props.values, 'body', '')}
                        onChange={(evt) => {
                          props.setFieldValue('body', evt.editor.getData());
                        }}
                        config={{
                          toolbar: [
                            ['Source'],
                            ['Styles', 'Format', 'Font', 'FontSize'],
                            ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript'],
                            ['Undo', 'Redo'],
                            ['EasyImageUpload'],
                            ['About'],
                          ],
                          extraPlugins: 'easyimage',
                          removePlugins: 'image',
                          autoParagraph: false,
                        }}
                      />
                    </div>
                  </Col>
                </Row>
                <Row>
                  <Col md={{ size: 12 }}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Description'}
                      isAsterisk
                      name="description"
                      onChange={(e) => {
                        props.handleChange(e);
                        props.setFieldTouched('description', true, true);
                      }}
                      type={'text'}
                      value={get(props.values, 'description', '')}
                    />
                  </Col>
                </Row>

                {
                  isSendMail && <Fragment>
                    <Row>
                      <Col md={{ size: 12 }}>
                        <div className="mt-3 mb-3" style={{ borderBottom: 'double' }}></div>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={{ size: 12 }}>
                        <FormCheckBox
                          label={'Send all customers'}
                          name={'allCustomer'}
                          value={get(props.values, 'allCustomer')}
                          onChange={(e) => {
                            props.handleChange(e);
                          }}
                          {...props}
                        />
                      </Col>
                    </Row>
                    <Row>
                      <Col md={{ size: 12 }}>
                        <FormInputGroup
                          didCheckErrors={false}
                          label={'Emails'}
                          name="emails"
                          onChange={(e) => {
                            props.handleChange(e);
                          }}
                          type={'text'}
                          value={get(props.values, 'emails', '')}
                          isTips
                          tips={'Separate emails with semicolons (;)'}
                        />
                      </Col>
                    </Row>
                  </Fragment>
                }

                <div className="d-flex flex-column align-items-center">
                  {
                    isSendMail ? <Button
                      blue
                      type="button"
                      className="min-width-300 mt-4"
                      onClick={() => this.handleSendMail(props.values)}
                    >Send mail</Button> :
                    <Button
                      primary
                      type="submit"
                      className="min-width-300 mt-4"
                      loading={props.isSubmitting}
                    >{selectedId === '' ? intl.formatMessage(messages.create) : intl.formatMessage(messages.update)}</Button>
                  }
                  <ButtonLink
                    onClick={handleOnClose}
                    type={'button'}
                  >{intl.formatMessage(messages.close)}</ButtonLink>
                </div>
              </Form>
            )}
          />
        </Wrapper>
      </StyledContainer>
    );
  }
}


ConfigurationPopup.propTypes = {};

const Wrapper = styled.div`
  margin-bottom: 10px;

  .content {
    border: 1px solid ${(props) => props.theme.colors.gray300};
    display: flex;
    flex-wrap: wrap;

    .label {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.small12};
      opacity: 0.5;
    }

    .bold-text {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.normal};
      font-weight: ${(props) => props.theme.fontWeights.strong};
      opacity: 0.8;
    }

    .group {
      padding: 10px 18px;
      background-color: ${(props) => props.theme.colors.white};
      width: 50%;

      &.gray {
        background-color: ${(props) => props.theme.colors.gray};
      }
    }
  }
`;


export default compose(
  WithHandleAlert,
  injectIntl
)(ConfigurationPopup);
