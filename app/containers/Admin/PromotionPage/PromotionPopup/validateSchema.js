import * as Yup from 'yup';
import messages from '../messages';

export default (intl) => (Yup.object().shape({
  code: Yup.string()
    .required(intl.formatMessage(messages.requiredNotNull)),
  description: Yup.string()
    .required(intl.formatMessage(messages.requiredNotNull)),
  status: Yup.string().nullable()
    .required(intl.formatMessage(messages.requiredNotNull)),
  discountType: Yup.string().nullable()
    .required(intl.formatMessage(messages.requiredNotNull)),
  discountValue: Yup.string().nullable()
    .required(intl.formatMessage(messages.requiredNotNull)),
  minAmount: Yup.string().nullable()
    .required(intl.formatMessage(messages.requiredNotNull)),
  maxDiscount: Yup.string().nullable()
    .required(intl.formatMessage(messages.requiredNotNull)),
  startTime: Yup.string().nullable()
    .required(intl.formatMessage(messages.requiredNotNull)),
  endTime: Yup.string().nullable()
    .required(intl.formatMessage(messages.requiredNotNull)),
}));
