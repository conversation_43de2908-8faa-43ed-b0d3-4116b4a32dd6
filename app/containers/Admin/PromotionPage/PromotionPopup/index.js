import React from 'react';
import { injectIntl } from 'react-intl';
import Button from 'components/common/Button';
import ButtonLink from 'components/ButtonLink';
import ActionDialog from 'components/common/ActionDialog';
import { TO, convertDropdownList } from 'utils/utilHelper';
import messages from '../messages';
import styled from 'styled-components';
import { Formik, Form } from 'formik';
import getSchema from './validateSchema';
import { Row, Col } from 'reactstrap';
import get from 'lodash/get';
import DropdownList from 'components/common/DropdownList';
import { compose } from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import { createPromotion, updatePromotion } from 'services/admin/promotion.service';
import FormCheckBox from 'components/common/FormCheckBox';
import FormInputGroup from 'components/common/FormInputGroup';
import auth from 'utils/auth';
import FormInputDatePicker from 'components/common/FormInputDatePicker';
import moment from 'moment';
import { permission } from 'constants/permission';
import { errorCode } from 'constants/responseCode';
import isEmpty from 'lodash/isEmpty';

const StyledContainer = styled(ActionDialog)`
  
`;

class PromotionPopup extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initData: {
        code: '',
        description: '',
        status: '',
        discountType: '',
        discountValue: null,
        minAmount: null,
        maxDiscount: null,
      },
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.selectedId !== '') {
      this.setState({
        initData: {
          ...nextProps.selectedObject,
          startTime: this.convertToDate(nextProps.selectedObject.startTime),
          endTime: this.convertToDate(nextProps.selectedObject.endTime),
        },
      });
    } else {
      const initData = {
        code: '',
        description: '',
        status: '',
        discountType: '',
        discountValue: null,
        minAmount: null,
        maxDiscount: null,
      };
      this.setState({
        initData,
      });
    }
  }

  convertToDate = (dateString) => {
    let convertDate = null;
    if (!isEmpty(dateString)) {
      convertDate = moment(dateString).utc();
      convertDate = convertDate.isValid() ? convertDate.toDate() : new Date(1970, 0, 1);
    }
    return convertDate;
  };

  handleSubmit = async (values, { setSubmitting }) => {
    const dataSubmit = {
      ...values,
    };

    if (this.props.selectedId !== '') {
      this.handleSubmitUpdate(dataSubmit, setSubmitting);
    } else {
      this.handleSubmitCreate(dataSubmit, setSubmitting);
    }
  }

  handleSubmitUpdate = async (dataSubmit, setSubmitting) => {
    const { selectedId, forceRefresh, handleOnClose, intl } = this.props;
    const [err, response] = await TO(updatePromotion(selectedId, dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.updatedError));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.updatedSuccess));
      handleOnClose();
      forceRefresh();
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.updatedError));
    }
    setSubmitting(false);
  }

  handleSubmitCreate = async (dataSubmit, setSubmitting) => {
    const { forceRefresh, handleOnClose, intl } = this.props;
    const [err, response] = await TO(createPromotion(dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.createdError));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.createdSuccess));
      handleOnClose();
      forceRefresh();
    } else if (response.message) {
      this.props.handleAlertError(response.message);
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.createdError));
    }
    setSubmitting(false);
  }

  render() {
    const self = this;
    const {
      intl,
      isOpen,
      handleOnClose,
      selectedId,
    } = this.props;

    const {
      initData,
    } = self.state;

    const discountTypeList = [
      { id: 'PERCENT', name: 'PERCENT' },
      { id: 'DIRECTLY', name: 'DIRECTLY' }];
    const statusList = [{ id: 'ACTIVE', name: 'Active' }, { id: 'INACTIVE', name: 'Inactive' }];

    return (
      <StyledContainer
        portalClassName="custom-portal"
        title={selectedId !== '' ? intl.formatMessage(messages.editLabel) : intl.formatMessage(messages.addLabel)}
        usePortal
        canOutsideClickClose
        canEscapeKeyClose
        isOpen={isOpen}
        onClose={handleOnClose}
      >
        <Wrapper className="m-4">
          <Formik
            onSubmit={this.handleSubmit}
            initialValues={initData}
            enableReinitialize
            validationSchema={getSchema(intl)}
            render={(props) => (
              <Form>
                <Row>
                  <Col md={{ size: 6 }}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Code'}
                      isAsterisk
                      name="code"
                      onChange={props.handleChange}
                      type={'text'}
                      value={get(props.values, 'code', [])}
                    />
                  </Col>
                  <Col md={{ size: 6 }}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Description'}
                      isAsterisk
                      name="description"
                      onChange={props.handleChange}
                      type={'text'}
                      value={get(props.values, 'description', [])}
                    />
                  </Col>
                </Row>
                <Row>
                  <Col md={{ size: 6 }}>
                    <DropdownList
                      label={'Discount Type'}
                      value={convertDropdownList(discountTypeList).find((option) =>
                        option.value === get(props.values, 'discountType')
                      )}
                      isAsterisk
                      name="discountType"
                      options={convertDropdownList(discountTypeList)}
                      {...props}
                    />
                  </Col>
                  <Col md={{ size: 6 }}>
                    <DropdownList
                      label={'Status'}
                      value={convertDropdownList(statusList).find((option) =>
                        option.value === get(props.values, 'status')
                      )}
                      isAsterisk
                      name="status"
                      options={convertDropdownList(statusList)}
                      {...props}
                    />
                  </Col>
                </Row>
                <Row>
                  <Col md={{ size: 6 }}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Discount Value'}
                      isAsterisk
                      name="discountValue"
                      onChange={props.handleChange}
                      type={'number'}
                      value={get(props.values, 'discountValue', [])}
                    />
                  </Col>
                  <Col md={{ size: 6 }}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Min Amount'}
                      isAsterisk
                      name="minAmount"
                      onChange={props.handleChange}
                      type={'number'}
                      value={get(props.values, 'minAmount', [])}
                    />
                  </Col>
                </Row>
                <Row>
                  <Col md={{ size: 6 }}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Max Discount'}
                      isAsterisk
                      name="maxDiscount"
                      onChange={props.handleChange}
                      type={'number'}
                      value={get(props.values, 'maxDiscount', [])}
                    />
                  </Col>
                </Row>
                <Row>
                  <Col md={{ size: 6 }}>
                    <FormInputDatePicker
                      name="startTime"
                      isAsterisk
                      value={get(props.values, 'startTime', null)}
                      label={'Start time'}
                      onChange={props.handleChange}
                      type="text"
                      {...props}
                    />
                  </Col>
                  <Col md={{ size: 6 }}>
                    <FormInputDatePicker
                      name="endTime"
                      isAsterisk
                      value={get(props.values, 'endTime', null)}
                      label={'End time'}
                      onChange={props.handleChange}
                      type="text"
                      {...props}
                    />
                  </Col>
                </Row>

                <div className="d-flex flex-column align-items-center">
                  {selectedId === '' ?
                    <Button
                      primary
                      type="submit"
                      className="min-width-300 mt-4"
                      loading={props.isSubmitting}
                    >{intl.formatMessage(messages.add)}</Button>
                    :
                    <Button
                      primary
                      type="submit"
                      className="min-width-300 mt-4"
                      loading={props.isSubmitting}
                    >{intl.formatMessage(messages.edit)}</Button>
                  }
                  <ButtonLink
                    onClick={handleOnClose}
                    type={'button'}
                  >{intl.formatMessage(messages.close)}</ButtonLink>
                </div>
              </Form>
           )}
          />
        </Wrapper>
      </StyledContainer>
    );
  }
}


PromotionPopup.propTypes = {
};

const Wrapper = styled.div`
  margin-bottom: 10px;

  .content {
    border: 1px solid ${(props) => props.theme.colors.gray300};
    display: flex;
    flex-wrap: wrap;

    .label {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.small12};
      opacity: 0.5;
    }

    .bold-text {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.normal};
      font-weight: ${(props) => props.theme.fontWeights.strong};
      opacity: 0.8;
    }

    .group {
      padding: 10px 18px;
      background-color: ${(props) => props.theme.colors.white};
      width: 50%;

      &.gray {
        background-color: ${(props) => props.theme.colors.gray};
      }
    }
  }
`;


export default compose(
  WithHandleAlert,
  injectIntl
)(PromotionPopup);
