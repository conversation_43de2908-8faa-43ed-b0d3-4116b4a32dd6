import { defineMessages } from 'react-intl';

const scope = 'app.components.common';

export default defineMessages({
  search: {
    id: `${scope}.search`,
    defaultMessage: 'Tìm kiếm',
  },
  allLabel: {
    id: `${scope}.allLabel`,
    defaultMessage: 'Tất cả',
  },
  searchPlaceHolder: {
    id: `${scope}.searchPlaceHolder`,
    defaultMessage: 'Mã, Tên',
  },
  no: {
    id: `${scope}.no`,
    defaultMessage: 'STT',
  },
  status: {
    id: `${scope}.status`,
    defaultMessage: 'Trạng thái',
  },
  action: {
    id: `${scope}.action`,
    defaultMessage: 'Thao tác',
  },
  update: {
    id: `${scope}.update`,
    defaultMessage: 'Cập nhật',
  },
  create: {
    id: `${scope}.create`,
    defaultMessage: 'Tạo',
  },
  close: {
    id: `${scope}.close`,
    defaultMessage: 'Đóng',
  },
  requiredNotNull: {
    id: `${scope}.requiredNotNull`,
    defaultMessage: 'Vui lòng nhập thông tin',
  },
  msgUpdateSuccess: {
    id: `${scope}.msgUpdateSuccess`,
    defaultMessage: 'Cập nhật thành công',
  },
  msgUpdateFailed: {
    id: `${scope}.msgUpdateFailed`,
    defaultMessage: 'Cập nhật thất bại.',
  },
  msgCreateSuccess: {
    id: `${scope}.msgCreateSuccess`,
    defaultMessage: 'Thêm mới thành công',
  },
  msgCreateFailed: {
    id: `${scope}.msgCreateFailed`,
    defaultMessage: 'Thêm mới thất bại',
  },
  msgDeleteSuccess: {
    id: `${scope}.msgDeleteSuccess`,
    defaultMessage: 'Xóa dữ liệu thành công',
  },
  msgDeleteFailed: {
    id: `${scope}.msgDeleteFailed`,
    defaultMessage: 'Không thể xóa dữ liệu này',
  },
  notFound: {
    id: `${scope}.notFound`,
    defaultMessage: 'Không tìm thấy dữ liệu',
  },
  clearAllFiltersButton: {
    id: `${scope}.clearAllFiltersButton`,
    defaultMessage: 'Xóa bộ lọc',
  },
  advancedSearchButton: {
    id: `${scope}.advancedSearchButton`,
    defaultMessage: 'Tìm kiếm nâng cao',
  },
  titleConfirm: {
    id: `${scope}.titleConfirm`,
    defaultMessage: 'Thông báo xác nhận',
  },
  messageConfirm: {
    id: `${scope}.messageConfirm`,
    defaultMessage: 'Bạn có muốn xóa dữ liệu này ?',
  },
  confirmButton: {
    id: `${scope}.confirmButton`,
    defaultMessage: 'Xác nhận',
  },
  cancelButton: {
    id: `${scope}.cancelButton`,
    defaultMessage: 'Hủy bỏ',
  },
  createdSuccess: {
    id: `${scope}.createdSuccess`,
    defaultMessage: 'Thêm mới thành công',
  },
  createdError: {
    id: `${scope}.createdError`,
    defaultMessage: 'Thêm mới thất bại',
  },
  updatedSuccess: {
    id: `${scope}.updatedSuccess`,
    defaultMessage: 'Cập nhật thành công',
  },
  updatedError: {
    id: `${scope}.updatedError`,
    defaultMessage: 'Cập nhật thất bại',
  },
  editLabel: {
    id: `${scope}.editLabel`,
    defaultMessage: 'Cập nhật thông tin',
  },
  addLabel: {
    id: `${scope}.addLabel`,
    defaultMessage: 'Tạo mới',
  },
  add: {
    id: `${scope}.add`,
    defaultMessage: 'Tạo',
  },
  edit: {
    id: `${scope}.edit`,
    defaultMessage: 'Cập nhật',
  },
});
