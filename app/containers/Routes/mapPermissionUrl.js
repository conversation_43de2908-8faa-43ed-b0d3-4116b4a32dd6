import { routes } from 'containers/Routes/routeHelper';

export const mapPermissionUrl = {
  NOTIFICATION: routes.NOTIFICATION,
  CLIENT_DASHBOARD: routes.CLIENT_DASHBOARD,
  CLIENT_NEW_MOBILE: routes.CLIENT_NEW_MOBILE,
  C<PERSON>IENT_PROXY: routes.CLIENT_PROXY,
  CLIENT_RECHARGE: routes.CLIENT_RECHARGE,
  CLIENT_ACCOUNT: routes.CLIENT_ACCOUNT,
  CLIENT_API: routes.CLIENT_API,
  CLIENT_TOOL: routes.CLIENT_TOOL,
  CLIENT_AFFILIATE: routes.CLIENT_AFFILIATE,
  CHANGE_PASSWORD: routes.CH<PERSON><PERSON>_PASSWORD,

  ADMIN_OVERVIEW: routes.ADMIN_OVERVIEW,
  ADMIN_MODEM: routes.ADMIN_MODEM,
  ADMIN_PROXY: routes.ADMIN_PROXY,
  ADMIN_CUSTOMER: routes.ADMIN_CUSTOMER,
  ADMIN_LICENSE: routes.ADMIN_LICENSE,
  ADMIN_PACKAGE: routes.ADMIN_PACKAGE,
  ADMIN_TRANSACTION: routes.ADMIN_TRANSACTION,
  ADMIN_CONFIGURATION: routes.ADMIN_CONFIGURATION,
  ADMIN_MONITOR: routes.ADMIN_MONITOR,
  ADMIN_PROMOTION: routes.ADMIN_PROMOTION,
  ADMIN_MAIL: routes.ADMIN_MAIL,
};
