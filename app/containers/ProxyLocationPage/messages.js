import { defineMessages } from 'react-intl';

const scope = 'app.components.common';

export default defineMessages({
  isp_network: {
    id: `${scope}.isp_network`,
    defaultMessage: 'ISP Network',
  },
  available: {
    id: `${scope}.available`,
    defaultMessage: 'Available',
  },
  not_available: {
    id: `${scope}.not_available`,
    defaultMessage: 'Not Available',
  },
  status: {
    id: `${scope}.status`,
    defaultMessage: 'Trạng thái',
  },
  buynow: {
    id: `${scope}.buynow`,
    defaultMessage: 'MUA NGAY',
  },
  proxy_location: {
    id: `${scope}.proxy_location`,
    defaultMessage: 'Proxy Location',
  },
  proxy_package: {
    id: `${scope}.proxy_package`,
    defaultMessage: 'Proxy Package',
  },
});
