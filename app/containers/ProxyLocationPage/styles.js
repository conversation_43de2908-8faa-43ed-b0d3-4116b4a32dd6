import styled from 'styled-components';
import background from 'images/background.png';

export default styled.div`
  background-image: url(${background});    
  background-position: center -200px;
  background-size: 100%;
  background-color: #f5f5f5;
  background-repeat: no-repeat;
  flex-grow: 1;
  
  .block {
    margin: 10% 10% 5% 5%;
  }
  
  .bp3-card {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding: 25px 10px 15px 10px;
    
    h7 {
      padding: 7px 1px 7px 1px;
      font-size: 14px;
    }
    .title {
        padding-bottom: 5px;
        font-size: 25px;
        font-weight: 600;
    }
    .amount {
        padding-top: 5px;
        font-size: 20px;
        font-weight: 600;
     }
  }
  
  .card-info {
    padding: 20px 0 10px 0;
  }
  
  .bp3-tab-indicator {
    height: 4px;
    background-image: linear-gradient(92deg, #11988d, #173c44);
  }
  
  .bp3-tab-list {
    .bp3-tab.tab {
      box-shadow: none !important;
      outline: none !important;
      text-transform: uppercase;      
      font-size: 14px;
      font-weight: 300;
      color: #000000;
      opacity: 0.5;
    
      &[aria-selected="true"] {
        opacity: 1;
      }
    }
  }
`;
