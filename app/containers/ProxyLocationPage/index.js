import React from 'react';
import { compose } from 'redux';
import Row from 'reactstrap/es/Row';
import Col from 'reactstrap/es/Col';
import Container from 'reactstrap/es/Container';
import { injectIntl } from 'react-intl';
import ProxyLocationItem from './ProxyLocationItem';
import StyledContainer from 'containers/ProxyLocationPage/styles';
import { getPackages } from 'services/admin/package.service';
import { getAllLocations } from 'services/user.service';
import messages from './messages';
import WithHandlePromise from 'containers/WithHandlePromise';
import PackageItem from '../Client/DashboardPage/PackageItem';
import { forwardTo } from '../../utils/history';
import { routes } from 'containers/Routes/routeHelper';
import BackButton from 'components/Transactions/BackButton';

export class ProxyLocation extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      locations: [],
    };
  }

  componentDidMount() {
  }

  componentWillMount() {
    this.handleGetLocations();
  }


  handleGetLocations = () => {
    this.props.handlePromise(getAllLocations(), (response) => {
      const { data } = response;
      this.setState({
        locations: data,
      });
    });
  }

  linkToBuy = () => {
    forwardTo('/login');
  }

  render() {
    const { intl } = this.props;
    const { locations } = this.state;

    return (
      <StyledContainer>
        <Container>
          <Row>
            <Col md={{ size: 12 }} className="pt-3 pb-2">
              <BackButton
                handleBack={() => this.linkToBuy(true)}
              />
            </Col>
          </Row>
          <Row>
            <Col md={{ size: 12 }} className="pl-5 pr-5 pt-3">
              <div>
                <h5>{intl.formatMessage(messages.proxy_location)}</h5>
                <div>
                  <Row>
                    { locations.map((location) => (
                      <Col md={{ size: 4 }} className="mb-3" style={{ paddingLeft: 5, paddingRight: 5 }}>
                        <ProxyLocationItem
                          location={location}
                          onClick={() => this.linkToBuy(true)}
                        />
                      </Col>)
                    )}
                  </Row>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </StyledContainer>
    );
  }
}


export default compose(
  injectIntl,
  WithHandlePromise,
)(ProxyLocation);
