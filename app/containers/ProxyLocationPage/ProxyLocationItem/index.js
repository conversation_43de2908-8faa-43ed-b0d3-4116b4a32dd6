import React, { Fragment } from 'react';
import { compose } from 'redux';
import { injectIntl } from 'react-intl';
import styled from 'styled-components';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import ProxyPageIcon from 'images/sidebarIcon/ic_currency.svg';
import { Card, Elevation } from '@blueprintjs/core';
import Button from 'components/common/Button';
import messages from '../messages';
import Row from 'reactstrap/es/Row';
import Col from 'reactstrap/es/Col';

const StyledComponent = styled.div`
  .info {
    width: 100%;
    text-align: center;
  }
  .header {
    font-weight: bold;  
  }
  .header:after {
    content: "";
    display: block;
    border-bottom: 1px solid #ccc;
    margin: 0 15px;
  }
`;


export class PackageItem extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      userInfo: {},
    };
  }

  render() {
    const {
      location,
      unit,
      onClick,
      intl,
    } = this.props;

    return (
      <StyledComponent>
        <Card
          interactive
          elevation={Elevation.TWO}
        >
          <div className="mb-5">
            <img src={ProxyPageIcon} height="50" width="50" />
          </div>
          <h5 className="title">{location.name}</h5>
          <Row className="info">
            <Col md={{ size: 6 }} className="mb-3 header col-6">
              {intl.formatMessage(messages.isp_network)}
            </Col>
            <Col md={{ size: 6 }} className="mb-3 header col-6">
              {intl.formatMessage(messages.status)}
            </Col>
            { location.isp.map((isp) => (
              <Fragment>
                <Col md={{ size: 6 }} className="mb-3 col-6">
                  {isp.name}
                </Col>
                <Col md={{ size: 6 }} className="mb-3 col-6">
                  {isp.status === 'READY'
                    ? <span style={{ color: 'green' }}>{intl.formatMessage(messages.available)}</span>
                    : <span style={{ color: 'red' }}>{intl.formatMessage(messages.not_available)}</span>}
                </Col>
              </Fragment>
              )
            )}
          </Row>

          {onClick &&
            <Button
              primary
              small
              type="button"
              className="mt-4 font-weight-bold text-white bg-primary"
              onClick={onClick}
              loading={false}
            >{intl.formatMessage(messages.buynow)}</Button>
          }
        </Card>
      </StyledComponent>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(PackageItem);
