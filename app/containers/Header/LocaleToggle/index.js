import React from 'react';
import { Menu, MenuItem, Popover, Position } from '@blueprintjs/core';
import vnFlag from 'images/icons/flag/vn-flag.png';
import enFlag from 'images/icons/flag/en-flag.png';
import russiaFlag from 'images/icons/flag/russia-flag.png';
import chinaFlag from 'images/icons/flag/china-flag.png';
import arabicFlag from 'images/icons/flag/arabic-flag.png';
import spainFlag from 'images/icons/flag/spain-flag.png';

import styled from 'styled-components';
import * as PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { createSelector } from 'reselect';
import { changeLocale } from '../../LanguageProvider/actions';
import { makeSelectLocale } from '../../LanguageProvider/selectors';
import breakpoint from '../../../styles/breakpoint';

const EN_LOCALE = 'en';
const VI_LOCALE = 'vi';
const RU_LOCALE = 'ru';
const CN_LOCALE = 'ca';
const AR_LOCALE = 'ar';
const SP_LOCALE = 'sn';

const StyledComponent = styled.div`
  position: absolute;
  right: 10px;
  top: 15px;
  
  @media (max-width: ${breakpoint.md}) {
    margin-right: 5px;
  }
`;

const StyledTarget = styled.div`
  a {
    img {
      border-radius: 50%;
      overflow: hidden;
      width: 26px;
      height: 26px;
      object-fit: cover;
      margin-right:5px;
    }
  }

`;

const StyledContent = styled.div`

`;

const StyledToggle = styled.div`
   .flag {
      width: auto;
      background: transparent;
      border-radius: 50%;
      border: none;
      cursor: pointer;
       img {
        border-radius: 50%;
        overflow: hidden;
        width: 26px;
        height: 26px;
        object-fit: cover;
      }
    }
`;

export class LocaleToggle extends React.PureComponent {
  onChangeLocale = (locale) => {
    this.props.onLocaleToggle(locale);
  };

  render() {
    const { locale } = this.props;

    const content = (
      <StyledContent>
        <Menu>
          <MenuItem
            text="English"
            icon={<img src={enFlag} alt={EN_LOCALE} style={{ height: 20 }} />}
            onClick={() => this.onChangeLocale(EN_LOCALE)}
          />
          <MenuItem
            text="Russia"
            icon={<img src={russiaFlag} alt={RU_LOCALE} style={{ height: 20 }} />}
            onClick={() => this.onChangeLocale(RU_LOCALE)}
          />
          <MenuItem
            text="Vietnam"
            icon={<img src={vnFlag} alt={VI_LOCALE} style={{ height: 20 }} />}
            onClick={() => this.onChangeLocale(VI_LOCALE)}
          />
          <MenuItem
            text="China"
            icon={<img src={chinaFlag} alt={CN_LOCALE} style={{ height: 20 }} />}
            onClick={() => this.onChangeLocale(CN_LOCALE)}
          />
          <MenuItem
            text="Spain"
            icon={<img src={spainFlag} alt={SP_LOCALE} style={{ height: 20 }} />}
            onClick={() => this.onChangeLocale(SP_LOCALE)}
          />
          <MenuItem
            text="Arabic"
            icon={<img src={arabicFlag} alt={AR_LOCALE} style={{ height: 20 }} />}
            onClick={() => this.onChangeLocale(AR_LOCALE)}
          />
        </Menu>
      </StyledContent>
    );

    const target = (
      <StyledTarget>
        { locale === EN_LOCALE && <a><img src={enFlag} alt={EN_LOCALE} /></a>}
        { locale === RU_LOCALE && <a><img src={russiaFlag} alt={RU_LOCALE} /></a>}
        { locale === VI_LOCALE && <a><img src={vnFlag} alt={VI_LOCALE} /></a>}
        { locale === CN_LOCALE && <a><img src={chinaFlag} alt={CN_LOCALE} /></a>}
        { locale === AR_LOCALE && <a><img src={arabicFlag} alt={AR_LOCALE} /></a>}
        { locale === SP_LOCALE && <a><img src={spainFlag} alt={SP_LOCALE} /></a>}
      </StyledTarget>);
    // const toggleFlag = (
    //   <StyledToggle>
    //     {locale === VI_LOCALE ?
    //       <button className={'flag'} onClick={() => this.onChangeLocale(EN_LOCALE)}>
    //         <img src={enFlag} alt={EN_LOCALE} />
    //       </button> :
    //       <button className={'flag'} onClick={() => this.onChangeLocale(VI_LOCALE)}>
    //         <img src={vnFlag} alt={VI_LOCALE} />
    //       </button>}
    //   </StyledToggle>
    // );
    return (
      <StyledComponent>
        <Popover content={content} target={target} position={Position.BOTTOM} />
      </StyledComponent>
    );
  }
}

LocaleToggle.propTypes = {
  onLocaleToggle: PropTypes.func,
  locale: PropTypes.string,
};

const mapStateToProps = createSelector(makeSelectLocale(), (locale) => ({
  locale,
}));

export function mapDispatchToProps(dispatch) {
  return {
    onLocaleToggle: (locale) => dispatch(changeLocale(locale)),
    dispatch,
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(LocaleToggle);
