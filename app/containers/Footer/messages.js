import { defineMessages } from 'react-intl';

const scope = 'app.components.Footer';

export default defineMessages({
  section_policy: {
    id: `${scope}.Footer.section_policy`,
    defaultMessage: 'CHÍNH SÁCH VÀ ĐIỀU KHOẢN',
  },
  section_partner: {
    id: `${scope}.Footer.section_partner`,
    defaultMessage: 'ĐỐI TÁC',
  },
  section_find_us: {
    id: `${scope}.Footer.section_find_us`,
    defaultMessage: 'KẾT NỐI VỚI RAPBANK',
  },
  section_support: {
    id: `${scope}.Footer.section_support`,
    defaultMessage: 'HỖ TRỢ',
  },

  terms_of_use: {
    id: `${scope}.Footer.terms_of_use`,
    defaultMessage: '<PERSON>i<PERSON>u khoản sử dụng',
  },
  privacy_policy: {
    id: `${scope}.Footer.privacy_policy`,
    defaultMessage: 'Chính sách riêng tư',
  },
  posting_policy: {
    id: `${scope}.Footer.posting_policy`,
    defaultMessage: '<PERSON><PERSON>h sách đăng tin',
  },
  cookie_policy: {
    id: `${scope}.Footer.cookie_policy`,
    defaultMessage: '<PERSON><PERSON>h sách cookie',
  },

  corporation: {
    id: `${scope}.Footer.corporation`,
    defaultMessage: 'Mời hợp tác',
  },
  bank: {
    id: `${scope}.Footer.bank`,
    defaultMessage: 'Ngân hàng',
  },
  banker: {
    id: `${scope}.Footer.banker`,
    defaultMessage: 'Nhân viên ngân hàng',
  },
  advertiser: {
    id: `${scope}.Footer.advertiser`,
    defaultMessage: 'Nhà quảng cáo',
  },

  introduction: {
    id: `${scope}.Footer.introduction`,
    defaultMessage: 'Giới thiệu',
  },
  recruitment: {
    id: `${scope}.Footer.recruitment`,
    defaultMessage: 'Tuyển dụng',
  },
  help: {
    id: `${scope}.Footer.help`,
    defaultMessage: 'Trợ giúp',
  },
  contact: {
    id: `${scope}.Footer.contact`,
    defaultMessage: 'Liên hệ',
  },
});
