/*
 * AccountManagementPage Messages
 *
 * This contains all the text for the Test component.
 */
import { defineMessages } from 'react-intl';

const scope = 'app.components.common';

export default defineMessages({
  username: {
    id: `${scope}.username`, defaultMessage: 'Username',
  },
  name: {
    id: `${scope}.name`, defaultMessage: 'Name',
  },
  email: {
    id: `${scope}.email`, defaultMessage: 'Email',
  },
  phone_number: {
    id: `${scope}.phone_number`, defaultMessage: 'Phone number',
  },
  received_email: {
    id: `${scope}.received_email`, defaultMessage: 'Received email',
  },
  change: {
    id: `${scope}.change`, defaultMessage: 'Change',
  },
  password: {
    id: `${scope}.password`, defaultMessage: 'Password',
  },
  profile: {
    id: `${scope}.profile`, defaultMessage: 'Profile',
  },
});
