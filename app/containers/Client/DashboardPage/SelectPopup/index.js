import React from 'react';
import { injectIntl } from 'react-intl';
import Button from 'components/common/Button';
import ButtonLink from 'components/ButtonLink';
import ActionDialog from 'components/common/ActionDialog';
import messages from '../messages';
import styled from 'styled-components';
import { Formik, Form } from 'formik';
import getSchema from './validateSchema';
import { Row, Col } from 'reactstrap';
import get from 'lodash/get';
import { compose } from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import FormRadioGroup from 'components/common/FormRadioGroup';
import { forwardTo } from 'utils/history';

const StyledContainer = styled(ActionDialog)`
  
`;

class SelectPopup extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initData: {
        method: 'CRYPTO',
      },
    };
  }

  handleSubmit = async (values, { setSubmitting }) => {
    const { purchaseData } = this.props;
    const { purchaseObj } = this.state;
    if (purchaseObj === undefined || purchaseObj.totalAmount === undefined) {
      return;
    }
    forwardTo(`/user/recharge?method=${values.method}&purchaseData=${btoa(purchaseData)}`);
    setSubmitting(false);
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.purchaseData !== undefined) {
      this.setState({
        purchaseObj: JSON.parse(nextProps.purchaseData),
      });
    }
  }


  render() {
    const self = this;
    const {
      intl,
      isOpen,
      handleOnClose,
    } = this.props;

    const {
      initData,
      purchaseObj,
    } = self.state;

    return (
      <StyledContainer
        portalClassName="custom-portal"
        title={'Please top-up'}
        usePortal
        canOutsideClickClose
        canEscapeKeyClose
        isOpen={isOpen}
        onClose={handleOnClose}
        width={550}
      >
        <Wrapper className="m-4">
          <Formik
            onSubmit={this.handleSubmit}
            initialValues={initData}
            enableReinitialize
            validationSchema={getSchema(intl)}
            render={(props) => (
              <Form>
                <Row>
                  <Col>
                    <h6>Your balance is not enough to create this transaction.<br/>
                      Please top-up add <span style={{ color: '#DC143C' }}>{purchaseObj.totalAmount}$</span> and make buy new proxy again.</h6>
                  </Col>
                </Row>
                <Row className="pt-2">
                  <Col>
                    <FormRadioGroup
                      options={[
                        {
                          value: 'CRYPTO',
                          label: '####WITH CRYPTO NOW',
                        },
                        {
                          value: 'CARD',
                          label: '####WITH YOUR CARD',
                        }]}
                      name={'method'}
                      label="####TOPUP YOUR ACCOUNT:"
                      selectedValue={get(props.values, 'method')}
                      {...props}
                      onChange={(value) => {
                        props.setFieldValue('method', value);
                      }}
                    />
                  </Col>
                </Row>

                <div className="d-flex flex-column align-items-center">
                  <div className="d-flex">
                    <Button
                      primary
                      type="submit"
                      className="min-width-100 mt-4 mr-1"
                      loading={props.isSubmitting}
                    >Create TOP-UP</Button>
                  </div>
                  <ButtonLink
                    onClick={handleOnClose}
                    type={'button'}
                  >{intl.formatMessage(messages.close)}</ButtonLink>
                </div>
              </Form>
            )}
          />
        </Wrapper>
      </StyledContainer>
    );
  }
}


SelectPopup.propTypes = {};

const Wrapper = styled.div`
  margin-bottom: 10px;

  .content {
    border: 1px solid ${(props) => props.theme.colors.gray300};
    display: flex;
    flex-wrap: wrap;

    .label {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.small12};
      opacity: 0.5;
    }

    .bold-text {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.normal};
      font-weight: ${(props) => props.theme.fontWeights.strong};
      opacity: 0.8;
    }

    .group {
      padding: 10px 18px;
      background-color: ${(props) => props.theme.colors.white};
      width: 50%;

      &.gray {
        background-color: ${(props) => props.theme.colors.gray};
      }
    }
  }
`;


export default compose(
  WithHandleAlert,
  injectIntl
)(SelectPopup);
