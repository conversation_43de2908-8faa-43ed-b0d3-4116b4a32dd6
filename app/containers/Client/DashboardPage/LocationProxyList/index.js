import React, { Fragment } from 'react';
import { compose } from 'redux';
import { injectIntl } from 'react-intl';
import styled from 'styled-components';
import messages from '../messages';
import Row from 'reactstrap/es/Row';
import Col from 'reactstrap/es/Col';
import PackageItem from '../PackageItem';

const StyledComponent = styled.div`
  .info {
    width: 100%;
    text-align: center;
  }
  .header {
    font-weight: bold;  
  }
  .header:after {
    content: "";
    display: block;
    border-bottom: 1px solid #ccc;
    margin: 0 15px;
  }
`;


export class LocationProxyList extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    const { intl, salePackages, togglePopup } = this.props;
    const wanPackageMinutes = salePackages.filter((i) => i.packageUnit === 'MINUTES');
    const wanPackageDays = salePackages.filter((i) => i.packageUnit === 'DAY');
    const wanPackageWeeks = salePackages.filter((i) => i.packageUnit === 'WEEK');
    const wanPackageMonths = salePackages.filter((i) => i.packageUnit === 'MONTH');


    return (
      <StyledComponent>
        <Row>
          { wanPackageMinutes.map((salePkg) => (
            <Col md={{ size: 4 }} className="mb-3" style={{ paddingLeft: 5, paddingRight: 5 }}>
              <PackageItem
                salePkg={salePkg}
                unit={'minutes'}
                onClick={() => togglePopup(true, salePkg.uuid)}
              />
            </Col>
                    )) }
          { wanPackageDays.map((salePkg) => (
            <Col md={{ size: 4 }} className="mb-3" style={{ paddingLeft: 5, paddingRight: 5 }}>
              <PackageItem
                salePkg={salePkg}
                unit={intl.formatMessage(messages.day)}
                onClick={() => togglePopup(true, salePkg.uuid)}
              />
            </Col>
                        )) }
          { wanPackageWeeks.map((salePkg) => (
            <Col md={{ size: 4 }} className="mb-3" style={{ paddingLeft: 5, paddingRight: 5 }}>
              <PackageItem
                salePkg={salePkg}
                unit={intl.formatMessage(messages.week)}
                onClick={() => togglePopup(true, salePkg.uuid)}
              />
            </Col>
                        )) }
          { wanPackageMonths.map((salePkg) => (
            <Col md={{ size: 4 }} className="mb-3" style={{ paddingLeft: 5, paddingRight: 5 }}>
              <PackageItem
                salePkg={salePkg}
                unit={intl.formatMessage(messages.month)}
                onClick={() => togglePopup(true, salePkg.uuid)}
              />
            </Col>
                        )) }
        </Row>
      </StyledComponent>
    );
  }
}

export default compose(
  injectIntl
)(LocationProxyList);
