import React, { Fragment } from 'react';
import { compose } from 'redux';
import { injectIntl } from 'react-intl';
import styled from 'styled-components';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import ProxyPageIcon from 'images/sidebarIcon/ic_currency.svg';
import { Card, Elevation } from '@blueprintjs/core';
import { formatCurrency } from 'utils/numberHelper';
import Button from 'components/common/Button';
import messages from '../messages';

const StyledComponent = styled.div`

`;


export class PackageItem extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      userInfo: {
      },
    };
  }

  render() {
    const {
      salePkg,
      unit,
      onClick,
      intl,
    } = this.props;

    return (
      <StyledComponent>
        <Card
          interactive
          elevation={Elevation.TWO}
        >
          <div className="mb-5">
            <img src={ProxyPageIcon} height="50" width="50" />
          </div>
          <h5 className="title">{salePkg.name}</h5>
          <h7 className="text-black-51"> ✔ {salePkg.packageUnit === 'DAY' ? `${salePkg.duration * 24} ${intl.formatMessage(messages.hour)}` : `${salePkg.duration} ${unit}` } {intl.formatMessage(messages.pkgInfo6)}</h7>
          <h7 className="text-black-51"> ✔ {intl.formatMessage(messages.pkgInfo1)}</h7>
          <h7 className="text-black-51"> ✔ {intl.formatMessage(messages.pkgInfo2)}</h7>
          <h7 className="text-black-51"> ✔ {intl.formatMessage(messages.pkgInfo3)}</h7>
          <h7 className="text-black-51"> ✔ {intl.formatMessage(messages.pkgInfo4)}</h7>
          <h7 className="text-black-51"> ✔ {intl.formatMessage(messages.pkgInfo5)} {salePkg.minTimeChangeIp >= 60 ? ` ${salePkg.minTimeChangeIp / 60} ${intl.formatMessage(messages.minute)}` : ` ${salePkg.minTimeChangeIp} ${intl.formatMessage(messages.second)}`}
          </h7>
          <h4 className="amount"> {formatCurrency(salePkg.price)} USD/{salePkg.duration === 1 ? '' : salePkg.duration}{unit}</h4>
          <Button
            primary
            small
            type="button"
            className="mt-4 font-weight-bold text-white bg-primary"
            onClick={onClick}
            loading={false}
          >{intl.formatMessage(messages.buynow)}</Button>
        </Card>
      </StyledComponent>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(PackageItem);
