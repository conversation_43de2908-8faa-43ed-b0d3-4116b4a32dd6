import React from 'react';
import { injectIntl } from 'react-intl';
import Button from 'components/common/Button';
import ButtonLink from 'components/ButtonLink';
import ActionDialog from 'components/common/ActionDialog';
import { TO, convertDropdownList } from 'utils/utilHelper';
import messages from '../messages';
import styled from 'styled-components';
import { Formik, Form } from 'formik';
import getSchema from './validateSchema';
import { Row, Col } from 'reactstrap';
import get from 'lodash/get';
import { compose } from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import { placeOrderProxy, redeemCode } from 'services/user.service';
import { errorCode } from 'constants/responseCode';
import FormInputGroup from 'components/common/FormInputGroup';
import FormMoneyInputGroup from 'components/common/FormMoneyInputGroup';
import FormRadioGroup from 'components/common/FormRadioGroup';
import { formatCurrency, generateAuthProxy } from 'utils/numberHelper';

const StyledContainer = styled(ActionDialog)`
  
`;

class PurchasePopup extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      isAuthenticateWithUsername: true,
      salePackage: {},
      initData: {
        authType: 'USERNAME',
        packageUuid: '',
        quantity: 1,
        time: 1,
        totalAmount: 0,
        username: '',
        whiteListIps: '',
        promotionCode: null,
        discountAmount: null,
      },
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.salePackage !== undefined) {
      const initData = {
        authType: 'USERNAME',
        packageUuid: '',
        quantity: 1,
        time: 1,
        totalAmount: nextProps.salePackage.price,
        username: generateAuthProxy(),
        whiteListIps: '',
        promotionCode: null,
        discountAmount: null,
      };
      this.setState({
        salePackage: {
          ...nextProps.salePackage,
        },
        initData,
      });
    }
  }

  handleSubmit = async (values, { setSubmitting }) => {
    const { forceRefresh, handleOnClose, intl, salePackage } = this.props;

    const dataSubmit = {
      packageUuid: salePackage.uuid,
      quantity: values.quantity,
      time: values.time,
      username: values.username,
      whiteListIps: values.whiteListIps,
      totalAmount: values.totalAmount,
      promotionCode: values.promotionCode,
      discountAmount: values.discountAmount,
    };

    const [err, response] = await TO(placeOrderProxy(dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.createdError));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.createdSuccess));
      handleOnClose();
      forceRefresh();
    } else if (response.message) {
      if (response.message === 'Insufficient balance') {
        const purchaseData = JSON.stringify(dataSubmit);
        this.props.toggleSelectPayment(true, purchaseData);
      } else {
        this.props.handleAlertError('Sorry, We are out of stock..');
      }
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.createdError));
    }
    setSubmitting(false);
  }

  handleRedeemCode = async (props, promotionCode, amount) => {
    if (promotionCode !== null && promotionCode === '') {
      this.handleResetPromotion(props);
      return;
    }

    const [err, response] = await TO(redeemCode(promotionCode, amount));
    this.setState({
      redeemResult: response.code,
    });

    if (response.code === errorCode.SUCCESS) {
      this.setState({
        redeemMessage: `Discount Amount: ${response.data}USD`,
      });
      props.setFieldValue('discountAmount', response.data);
      this.handleTotalAmount(
        props,
        get(props.values, 'time', 0),
        get(props.values, 'quantity', 0),
        response.data
      );
    } else {
      this.setState({
        redeemMessage: response.message,
      });
      props.setFieldValue('discountAmount', 0);
      this.handleTotalAmount(
        props,
        get(props.values, 'time', 0),
        get(props.values, 'quantity', 0),
        0
      );
    }
  }

  handleTotalAmount = (props, time, quantity, discountAmount) => {
    const {
      salePackage,
    } = this.state;
    props.setFieldValue('totalAmount', (salePackage.price * time * quantity) - discountAmount);
  }

  handleResetPromotion = (props) => {
    this.setState({
      redeemResult: null,
      redeemMessage: null,
    });
    props.setFieldValue('discountAmount', 0);
    props.setFieldValue('promotionCode', '');
    props.setFieldTouched('promotionCode', true, true);
  }

  render() {
    const self = this;
    const {
      intl,
      isOpen,
      handleOnClose,
      selectedId,
    } = this.props;

    const {
      initData,
      salePackage,
      isAuthenticateWithUsername,
      redeemResult,
      redeemMessage,
    } = self.state;

    const timeLabel = salePackage.packageUnit === 'DAY'
      ? intl.formatMessage(messages.day) : salePackage.packageUnit === 'WEEK'
        ? intl.formatMessage(messages.week) : intl.formatMessage(messages.month);

    return (
      <StyledContainer
        portalClassName="custom-portal"
        title={`${intl.formatMessage(messages.purchaseTitle)} ${salePackage.name}`}
        usePortal
        canOutsideClickClose
        canEscapeKeyClose
        isOpen={isOpen}
        onClose={handleOnClose}
        width={550}
      >
        <Wrapper className="m-4">
          <Formik
            onSubmit={this.handleSubmit}
            initialValues={initData}
            enableReinitialize
            validationSchema={getSchema(intl, isAuthenticateWithUsername)}
            render={(props) => (
              <Form>
                <Row>
                  <Col md={{ size: 12 }}>
                    <FormMoneyInputGroup
                      didCheckErrors={false}
                      label={intl.formatMessage(messages.number_of_proxy)}
                      isAsterisk
                      name="quantity"
                      onChange={(e) => {
                        props.handleChange(e);
                        props.setFieldTouched('quantity', true, true);
                        this.handleResetPromotion(props);
                        this.handleTotalAmount(
                          props,
                          get(props.values, 'time', 0),
                          e.target.value,
                          0,
                        );
                      }}
                      type={'number'}
                      value={get(props.values, 'quantity', [])}
                    />
                  </Col>
                </Row>
                <Row>
                  <Col md={{ size: 12 }}>
                    <FormMoneyInputGroup
                      didCheckErrors={false}
                      label={timeLabel}
                      isAsterisk
                      name="time"
                      onChange={(e) => {
                        props.handleChange(e);
                        props.setFieldTouched('time', true, true);
                        this.handleResetPromotion(props);
                        this.handleTotalAmount(
                          props,
                          e.target.value,
                          get(props.values, 'quantity', 0),
                          0,
                        );
                      }}
                      type={'number'}
                      value={get(props.values, 'time', [])}
                    />
                  </Col>
                </Row>
                <Row>
                  <Col>
                    <FormRadioGroup
                      options={[{
                        value: 'USERNAME',
                        label: 'Username:Password',
                      },
                      {
                        value: 'IP',
                        label: 'IP Whitelist',
                      }]}
                      name={'authType'}
                      label={`${intl.formatMessage(messages.proxy_auth_by)}:`}
                      selectedValue={get(props.values, 'authType')}
                      {...props}
                      onChange={(value) => {
                        props.setFieldValue('authType', value);
                        props.setFieldValue('username', generateAuthProxy());
                        props.setFieldValue('whiteListIps', '');
                        this.setState({
                          isAuthenticateWithUsername: value === 'USERNAME',
                        });
                      }}
                    />
                  </Col>
                </Row>

                <Row>
                  <Col md={{ size: 12 }}>
                    {isAuthenticateWithUsername ?
                      <FormInputGroup
                        didCheckErrors={false}
                        label={'Username:Password'}
                        name="username"
                        isTips
                        disabled
                        tips={intl.formatMessage(messages.tips_password_random)}
                        onChange={(e) => {
                          props.handleChange(e);
                          props.setFieldTouched('username', true, true);
                        }}
                        type={'text'}
                        value={`${get(props.values, 'username', '')}`}
                        placeholder={intl.formatMessage(messages.password_random)}
                        isRefreshButton
                        handleRefreshFunc={() => {
                          props.setFieldTouched('username', true, true);
                          props.setFieldValue('username', generateAuthProxy());
                        }}
                      />
                      :
                      <FormInputGroup
                        didCheckErrors={false}
                        label={'IP White list'}
                        name="whiteListIps"
                        isAsterisk
                        isTips
                        tips={intl.formatMessage(messages.ip_whitelist)}
                        onChange={(e) => {
                          props.handleChange(e);
                          props.setFieldTouched('whiteListIps', true, true);
                        }}
                        type={'text'}
                        value={get(props.values, 'whiteListIps', '')}
                        placeholder={'IP1,IP2'}
                      />
                    }
                  </Col>
                </Row>
                <Row>
                  <Col md={{ size: 12 }}>
                    <FormInputGroup
                      didCheckErrors={false}
                      label={'Promotion Code'}
                      name="promotionCode"
                      type={'text'}
                      value={get(props.values, 'promotionCode', [])}
                      onChange={(e) => {
                        props.setFieldValue('promotionCode', e.target.value);
                        this.handleRedeemCode(props, e.target.value, get(props.values, 'totalAmount', 0));
                      }}
                      isMessageSuccess={redeemResult === errorCode.SUCCESS ? redeemMessage : null}
                      isMessageFailed={redeemResult === errorCode.FAILED ? redeemMessage : null}
                    />
                  </Col>
                </Row>
                <Row>
                  <Col md={{ size: 12 }}>
                    <FormInputGroup
                      disabled
                      label={`${intl.formatMessage(messages.total_amount)} (USD)`}
                      name="totalAmount"
                      type={'text'}
                      value={`${formatCurrency(get(props.values, 'totalAmount', 0))}USD`}
                      placeHolder={''}
                    />
                  </Col>
                </Row>
                <div className="d-flex flex-column align-items-center">
                  {selectedId === '' ?
                    <Button
                      primary
                      type="submit"
                      className="min-width-300 mt-2"
                      loading={false}
                    >{intl.formatMessage(messages.add)}</Button>
                    :
                    <div className="d-flex">
                      <Button
                        primary
                        type="submit"
                        className="min-width-100 mt-2 mr-1"
                        loading={props.isSubmitting}
                      >{intl.formatMessage(messages.buy)}</Button>
                    </div>
                  }
                  <ButtonLink
                    onClick={handleOnClose}
                    type={'button'}
                  >{intl.formatMessage(messages.close)}</ButtonLink>
                </div>
              </Form>
           )}
          />
        </Wrapper>
      </StyledContainer>
    );
  }
}


PurchasePopup.propTypes = {
};

const Wrapper = styled.div`
  margin-bottom: 10px;

  .content {
    border: 1px solid ${(props) => props.theme.colors.gray300};
    display: flex;
    flex-wrap: wrap;

    .label {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.small12};
      opacity: 0.5;
    }

    .bold-text {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.normal};
      font-weight: ${(props) => props.theme.fontWeights.strong};
      opacity: 0.8;
    }

    .group {
      padding: 10px 18px;
      background-color: ${(props) => props.theme.colors.white};
      width: 50%;

      &.gray {
        background-color: ${(props) => props.theme.colors.gray};
      }
    }
  }
`;


export default compose(
  WithHandleAlert,
  injectIntl
)(PurchasePopup);
