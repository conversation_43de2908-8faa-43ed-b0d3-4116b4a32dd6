import React, { Fragment } from 'react';
import StyledContainer from 'containers/Client/DashboardPage/styles';
import { forwardTo } from '../../../utils/history';
import { routes } from 'containers/Routes/routeHelper';
import { injectIntl } from 'react-intl';
import { compose } from 'redux';
import messages from './messages';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import { Card, Tab } from '@blueprintjs/core';
import { Row, Col } from 'reactstrap';
import { getPackages } from 'services/admin/package.service';
import { getUserBalance, getAllLocations } from 'services/user.service';
import { formatCurrency } from 'utils/numberHelper';
import PurchasePopup from './PurchasePopup';
import SelectPopup from './SelectPopup';
import ProxyLocationItem from '../../ProxyLocationPage/ProxyLocationItem';
import CommonTabs from 'components/common/Tabs';
import LocationProxyList from './LocationProxyList';


export class OverviewPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      dataList: [],
      salePackages: [],
      locations: [],
      salePackage: null,
      isOpenPopup: false,
      forceRefresh: false,
      config: {
        balance: 0,
        totalLicense: 0,
        totalExpiredLicense: 0,
      },
    };
  }

  componentWillReceiveProps(nextProps) {
  }

  componentWillMount() {
    this.handleGetUserInfo();
    this.handleGetPackages();
    this.handleGetLocations();
  }

  handleGetLocations = () => {
    this.props.handlePromise(getAllLocations(), (response) => {
      const { data } = response;
      this.setState({
        locations: data,
      });
    });
  }

  handleGetPackages = () => {
    const requestBody = {
      filtered: [{
        id: 'status',
        value: 'ACTIVE',
      }],
      pageSize: 20,
      page: 0,
    };
    this.props.handlePromise(getPackages(requestBody), (response) => {
      const { data } = response.data;
      this.setState({
        salePackages: data,
      });
    });
  }

  handleGetUserInfo = () => {
    this.props.handlePromise(getUserBalance(), (response) => {
      this.setState({
        config: {
          ...response,
        },
      });
    });
  }

  togglePopup = (isOpen = true, selectedId) => {
    if (isOpen) {
      const { salePackages } = this.state;
      const selectedObject = salePackages.find((item) => item.uuid === selectedId);

      this.setState({
        selectedId,
        selectedObject,
      });
    } else {
      this.handleGetUserInfo();
    }
    this.setState({
      isOpenPopup: isOpen,
    });
  }

  toggleSelectPayment = (isOpen = true, purchaseData) => {
    this.setState({
      isOpenSelect: isOpen,
      purchaseData,
    });
  }


  render() {
    const { intl } = this.props;
    const { salePackages, isOpenPopup, isOpenSelect, selectedObject, config, purchaseData, locations } = this.state;

    const tabs = [];
    locations.forEach((location, i) => {
      tabs.push({
        id: i,
        title: location.name,
        panel: <LocationProxyList
          salePackages={salePackages.filter((i) => i.location === location.name)}
          togglePopup={this.togglePopup}
        />
        ,
      });
    });

    return (
      <StyledContainer>
        <Fragment>
          <Row className="p-12 mt-3">
            <Col md={{ size: 12 }} className="pl-5 pr-5 pt-1">
              <div>
                <Row>
                  <Col md={{ size: 3 }}>
                    <Card interactive className="bg-success card-info" onClick={() => forwardTo(routes.CLIENT_RECHARGE)}>
                      <h6 className="text-white">{intl.formatMessage(messages.balance)}: <b>{formatCurrency(config.balance)} USD</b></h6>
                    </Card>
                  </Col>
                  <Col md={{ size: 3 }}>
                    <Card interactive className="bg-success card-info" onClick={() => forwardTo(routes.CLIENT_PROXY)}>
                      <h6 className="text-white">{intl.formatMessage(messages.total_license)}: <b>{config.totalLicense}</b></h6>
                    </Card>
                  </Col>
                  <Col md={{ size: 4 }}>
                    <Card interactive className="bg-success card-info" onClick={() => forwardTo(routes.CLIENT_PROXY)}>
                      <h6 className="text-white">{intl.formatMessage(messages.total_license_expired)}: <b>{config.totalExpiredLicense}</b></h6>
                    </Card>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
          <Row>
            <Col md={{ size: 12 }} className="pl-5 pr-5 pt-4">
              <div>
                <h5>{intl.formatMessage(messages.proxy_location)}</h5>
                <div>
                  <Row>
                    { locations.map((location) => (
                      <Col md={{ size: 4 }} className="mb-3" style={{ paddingLeft: 5, paddingRight: 5 }}>
                        <ProxyLocationItem
                          location={location}
                        />
                      </Col>)
                    )}
                  </Row>
                </div>
              </div>
            </Col>
          </Row>
          <Row>
            <Col md={{ size: 12 }} className="pl-5 pr-5 pt-3">
              <div>
                <h5>{intl.formatMessage(messages.proxy_package)}</h5>


                <CommonTabs
                  selectedTabId={'0'}
                  defaultSelectedTabId={'1'}
                >
                  {tabs.map((item) => (
                    <Tab
                      className="tab"
                      id={item.id}
                      title={item.title}
                      panel={item.panel}
                      ref={(ref) => { this.tabButton = ref; }}
                    />
                  ))}
                </CommonTabs>


              </div>
            </Col>

          </Row>
          <PurchasePopup
            isOpen={isOpenPopup}
            salePackage={selectedObject}
            handleOnClose={() => this.togglePopup(false)}
            forceRefresh={this.forceRefresh}
            toggleSelectPayment={this.toggleSelectPayment}
          />
          <SelectPopup
            isOpen={isOpenSelect}
            purchaseData={purchaseData}
            handleOnClose={() => this.toggleSelectPayment(false)}
          />

        </Fragment>
      </StyledContainer>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(OverviewPage);
