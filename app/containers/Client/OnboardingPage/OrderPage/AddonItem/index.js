import React, {Fragment} from 'react';
import {compose} from 'redux';
import {injectIntl} from 'react-intl';
import styled from 'styled-components';
import WithHandlePromise from '../../../../WithHandlePromise';
import WithHandleAlert from '../../../../WithHandleAlert';
import {Card, Elevation} from '@blueprintjs/core';
import messages from '../../messages';
import CheckIcon from '../../../../../images/check-ico.png';

const StyledComponent = styled.div`
  h7 {
    padding: 1px 1px 0px 1px !important;
  }
  hr {
    border: none;
    border-top: 1px dotted #f00;
    color: #fff;
    background-color: #fff;
    height: 1px;
    width: 50%;
  }

  .check-box {
    position: absolute;
    top: 15px;
    right: 15px;

    &.show {
      display: inline;
    }

    &.hide {
      display: none;
    }
  }

`;


export class AddonItem extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      userInfo: {},
    };
  }

  render() {
    const {
      onClick,
      intl,
      icon,
      addon,
      value,
    } = this.props;

    return (
      <StyledComponent>
        <Card
          interactive
          elevation={Elevation.TWO}
          onClick={onClick}
        >
          <div className="mb-1">
            <img src={icon} height="100" width="100"/>
            <div className={addon === value ? 'check-box show' : 'check-box hide'}>
              <img src={CheckIcon} height="50" width="50"/>
            </div>
          </div>
          <div className="d-flex flex-column">
          </div>
        </Card>
      </StyledComponent>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(AddonItem);
