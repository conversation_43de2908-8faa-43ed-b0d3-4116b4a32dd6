import styled from 'styled-components';

export default styled.div`
  table {
    .title {
      width: 60%;
      font-size: 16px;
      font-weight: bold;
      text-align: right;
    }

    .content {
      font-weight: 400;
      padding-top: 5px;
      padding-left: 10px;
      font-size: 16px;
    }
  }

  .row {
    width: 100%;
  }


  .item-callout {
    border-radius: .25rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, .12), 0 1px 2px rgba(0, 0, 0, .24);
    background-color: #fff;
    border-left: 5px solid #e9ecef;
    margin-bottom: 0.5rem;
    padding: 1rem;
    border-left-color: #1e7e34;
  }

  .callout{
    border-radius: .25rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, .12), 0 1px 2px rgba(0, 0, 0, .24);
    background-color: #fff;
    border-left: 5px solid #e9ecef;
    margin-bottom: 0.5rem;
    padding: 1rem;
  }

  .callout.callout-danger {
    border-left-color: #bd2130;
  }

  .callout.callout-info {
    border-left-color: #117a8b;
  }

  .callout.callout-warning {
    border-left-color: #d39e00;
  }

  .callout.callout-success {
    border-left-color: #1e7e34;
  }

  .card-header {
    background-color: transparent;
    padding: .1rem .5rem;
    position: relative;
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
    border-bottom: 0;
  }

  .container-fluid {
    padding-left: 0px;
    padding-right: 0px;
  }

`;
