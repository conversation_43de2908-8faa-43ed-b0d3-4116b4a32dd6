import React, {Fragment} from 'react';
import {compose} from 'redux';
import {injectIntl} from 'react-intl';
import styled from 'styled-components';
import WithHandlePromise from '../../../../WithHandlePromise';
import WithHandleAlert from '../../../../WithHandleAlert';
import ProxyPageIcon from '../../../../../images/sidebarIcon/ic_currency.svg';
import {Card, Elevation} from '@blueprintjs/core';
import {formatCurrency} from '../../../../../utils/numberHelper';
import Button from '../../../../../components/common/Button';
import messages from '../../messages';
import CheckIcon from '../../../../../images/check-ico.png';
import get from 'lodash/get';
import first from "lodash/first";

const StyledComponent = styled.div`
  h7 {
    padding: 1px 1px 0px 1px !important;
  }

  hr {
    border: none;
    border-top: 1px dotted #f00;
    color: #fff;
    background-color: #fff;
    height: 1px;
    width: 50%;
  }

  .bp3-card {
    padding: 5px;
  }

  .available {
  }

  .not-available {
    background-color: #999999;
  }

  .check-box {
    position: absolute;
    top: 15px;
    right: 15px;

    &.show {
      display: inline;
    }

    &.hide {
      display: none;
    }
  }

`;


export class ISPItem extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      userInfo: {},
    };
  }

  render() {
    const {
      onClick,
      intl,
      icon,
      isp,
      location,
      value,
      status,
      salePackages
    } = this.props;

    let salePkg = {};
    if (salePackages) {
      salePkg = salePackages.find((el) => {
        return el.isp === isp && el.location === location;
      });
    }

    return (
      <StyledComponent>
        <Card
          interactive
          elevation={Elevation.TWO}
          className={status === 'available' ? 'available' : 'not-available'}
          onClick={status === 'available' ? onClick : () => {
          }}
        >
          <div className="mb-1">
            <img src={icon} height="100" width="100"/>
            <div className={isp === value ? 'check-box show' : 'check-box hide'}>
              <img src={CheckIcon} height="50" width="50"/>
            </div>
          </div>

          <div className="d-flex flex-column">

            <h7 className="text-black-51"> ✔ Dedicated SIM card</h7>
            <h7 className="text-black-51"> ✔ {intl.formatMessage(messages.pkgInfo1)}</h7>
            <h7 className="text-black-51"> ✔ {intl.formatMessage(messages.pkgInfo2)}</h7>

            <h7 className="text-black-51"> ✔ Support IPv4/IPv6</h7>
            <h7 className="text-black-51"> ✔ Support VPN/UDP</h7>
            <h7 className="text-black-51"> ✔ Auth. User: Pass / IP Whitelist</h7>
            <h7 className="text-black-51"> ✔ Set Rotation Time / API to change IP</h7>
          </div>
          <h4
            className="amount"> Status: <span
            style={{textTransform: 'capitalize', color: status === 'available' ? 'green' : 'red'}}>{status}</span></h4>
          <hr/>
          <h4
            className="amount"> DL/UP:
            <span style={{color: "green"}}> {get(salePkg, 'speedTestDownloadBytes')}Mbps</span>/
            <span style={{color: "red"}}>{get(salePkg, 'speedTestUploadBytes')}Mbps</span></h4>
          <h7 className="text-black-51"> Last Perform: {get(salePkg, 'speedTestTime')}</h7>
        </Card>
      </StyledComponent>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(ISPItem);
