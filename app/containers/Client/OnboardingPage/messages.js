import { defineMessages } from 'react-intl';

const scope = 'app.components.common';

export default defineMessages({
  purchaseTitle: {
    id: `${scope}.purchaseTitle`,
    defaultMessage: 'Mua mới',
  },
  search: {
    id: `${scope}.search`,
    defaultMessage: 'Tìm kiếm',
  },
  allLabel: {
    id: `${scope}.allLabel`,
    defaultMessage: 'Tất cả',
  },
  searchPlaceHolder: {
    id: `${scope}.searchPlaceHolder`,
    defaultMessage: 'Mã, Tên',
  },
  no: {
    id: `${scope}.no`,
    defaultMessage: 'STT',
  },
  status: {
    id: `${scope}.status`,
    defaultMessage: 'Trạng thái',
  },
  action: {
    id: `${scope}.action`,
    defaultMessage: 'Thao tác',
  },
  update: {
    id: `${scope}.update`,
    defaultMessage: 'Cập nhật',
  },
  create: {
    id: `${scope}.create`,
    defaultMessage: 'Tạo',
  },
  close: {
    id: `${scope}.close`,
    defaultMessage: 'Đóng',
  },
  requiredNotNull: {
    id: `${scope}.requiredNotNull`,
    defaultMessage: 'Vui lòng nhập thông tin',
  },
  msgUpdateSuccess: {
    id: `${scope}.msgUpdateSuccess`,
    defaultMessage: 'Cập nhật thành công',
  },
  msgUpdateFailed: {
    id: `${scope}.msgUpdateFailed`,
    defaultMessage: 'Cập nhật thất bại.',
  },
  msgCreateSuccess: {
    id: `${scope}.msgCreateSuccess`,
    defaultMessage: 'Thêm mới thành công',
  },
  msgCreateFailed: {
    id: `${scope}.msgCreateFailed`,
    defaultMessage: 'Thêm mới thất bại',
  },
  msgDeleteSuccess: {
    id: `${scope}.msgDeleteSuccess`,
    defaultMessage: 'Xóa dữ liệu thành công',
  },
  msgDeleteFailed: {
    id: `${scope}.msgDeleteFailed`,
    defaultMessage: 'Không thể xóa dữ liệu này',
  },
  notFound: {
    id: `${scope}.notFound`,
    defaultMessage: 'Không tìm thấy dữ liệu',
  },
  clearAllFiltersButton: {
    id: `${scope}.clearAllFiltersButton`,
    defaultMessage: 'Xóa bộ lọc',
  },
  advancedSearchButton: {
    id: `${scope}.advancedSearchButton`,
    defaultMessage: 'Tìm kiếm nâng cao',
  },
  titleConfirm: {
    id: `${scope}.titleConfirm`,
    defaultMessage: 'Thông báo xác nhận',
  },
  messageConfirm: {
    id: `${scope}.messageConfirm`,
    defaultMessage: 'Bạn có muốn xóa dữ liệu này ?',
  },
  confirmButton: {
    id: `${scope}.confirmButton`,
    defaultMessage: 'Xác nhận',
  },
  cancelButton: {
    id: `${scope}.cancelButton`,
    defaultMessage: 'Hủy bỏ',
  },
  createdSuccess: {
    id: `${scope}.createdSuccess`,
    defaultMessage: 'Mua mới thành công. Vui lòng kiểm tra tại mục My Proxy!',
  },
  createdError: {
    id: `${scope}.createdError`,
    defaultMessage: 'Mua mới thất bại. Vui lòng thử lại.',
  },
  updatedSuccess: {
    id: `${scope}.updatedSuccess`,
    defaultMessage: 'Cập nhật thành công',
  },
  updatedError: {
    id: `${scope}.updatedError`,
    defaultMessage: 'Cập nhật thất bại',
  },
  editLabel: {
    id: `${scope}.editLabel`,
    defaultMessage: 'Cập nhật thông tin',
  },
  addLabel: {
    id: `${scope}.addLabel`,
    defaultMessage: 'Tạo mới',
  },
  add: {
    id: `${scope}.add`,
    defaultMessage: 'Tạo',
  },
  edit: {
    id: `${scope}.edit`,
    defaultMessage: 'Cập nhật',
  },
  pkgInfo1: {
    id: `${scope}.pkgInfo1`,
    defaultMessage: 'Hỗ trợ',
  },
  pkgInfo2: {
    id: `${scope}.pkgInfo2`,
    defaultMessage: 'Loại Proxy',
  },
  pkgInfo3: {
    id: `${scope}.pkgInfo3`,
    defaultMessage: 'Không Giới Hạn Đổi IP',
  },
  pkgInfo4: {
    id: `${scope}.pkgInfo4`,
    defaultMessage: 'Không Giới Hạn Vị Trí',
  },
  pkgInfo5: {
    id: `${scope}.pkgInfo5`,
    defaultMessage: 'Thời gian tối thiểu đổi IP',
  },
  pkgInfo6: {
    id: `${scope}.pkgInfo6`,
    defaultMessage: 'sử dụng SIM riêng biệt',
  },
  minute: {
    id: `${scope}.minute`,
    defaultMessage: 'phút',
  },
  second: {
    id: `${scope}.second`,
    defaultMessage: 'giây',
  },
  day: {
    id: `${scope}.day`,
    defaultMessage: 'ngày',
  },
  month: {
    id: `${scope}.month`,
    defaultMessage: 'tháng',
  },
  week: {
    id: `${scope}.week`,
    defaultMessage: 'tuần',
  },
  hour: {
    id: `${scope}.hour`,
    defaultMessage: 'giờ',
  },
  buynow: {
    id: `${scope}.buynow`,
    defaultMessage: 'MUA NGAY',
  },
  balance: {
    id: `${scope}.buynow`,
    defaultMessage: 'Balance',
  },
  total_license: {
    id: `${scope}.total_license`,
    defaultMessage: 'Total license',
  },
  total_license_expired: {
    id: `${scope}.total_license_expired`,
    defaultMessage: 'Total license expires today',
  },
  proxy_location: {
    id: `${scope}.proxy_location`,
    defaultMessage: 'Proxy Location',
  },
  proxy_package: {
    id: `${scope}.proxy_package`,
    defaultMessage: 'Proxy Package',
  },
  number_of_proxy: {
    id: `${scope}.number_of_proxy`,
    defaultMessage: 'Number of proxies',
  },
  proxy_auth_by: {
    id: `${scope}.proxy_auth_by`,
    defaultMessage: 'Proxy authentication by',
  },
  tips_password_random: {
    id: `${scope}.tips_password_random`,
    defaultMessage: 'Password will be randomly generated for each port. You can change it during use.',
  },
  password_random: {
    id: `${scope}.password_random`,
    defaultMessage: 'Password will be generated randomly for each port.',
  },
  ip_whitelist: {
    id: `${scope}.ip_whitelist`,
    defaultMessage: 'IP whitelist: IP1,IP2,.... You can change during use.',
  },
  total_amount: {
    id: `${scope}.total_amount`,
    defaultMessage: 'Total amount',
  },
  buy: {
    id: `${scope}.buy`,
    defaultMessage: 'Buy',
  },
  input_amount: {
    id: `${scope}.input_amount`, defaultMessage: 'Input Amount',
  },
  min_amount: {
    id: `${scope}.min_amount`, defaultMessage: 'Min Amount',
  },
  est_amount: {
    id: `${scope}.est_amount`, defaultMessage: 'Estimate Amount',
  },
  msgGetMinFailed: {
    id: 'app.components.common.msgGetMinFailed',
    defaultMessage: 'Can not get minimum amount of this curreny. Please choose another currency',
  },
});
