import React, {Fragment} from 'react';
import {compose} from 'redux';
import {injectIntl} from 'react-intl';
import styled from 'styled-components';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import {Card, Elevation} from '@blueprintjs/core';
import messages from '../../messages';
import CheckIcon from 'images/check-ico.png';

const StyledComponent = styled.div`
  position: relative;

  .check-box {
    position: absolute;
    top: 5px;
    right: 20px;

    &.show {
      display: inline;
    }

    &.hide {
      display: none;
    }
  }
`;


export class Coin extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      userInfo: {},
    };
  }

  render() {
    const {
      onClick,
      intl,
      currency,
      coin
    } = this.props;

    console.log(coin + '----' + currency.code);

    return (
      <StyledComponent>
        <Card
          className="mr-3 p-3"
          interactive
          elevation={Elevation.TWO}
          onClick={onClick}
        >
          <div className="d-flex flex-column align-items-center">
            <img src={`https://nowpayments.io/${currency.logo_url}`} height="40"/>
            <div className={coin === currency.code ? 'check-box show' : 'check-box hide'}>
              <img src={CheckIcon} height="23" width="23"/>
            </div>
            <h6 className="mt-3">{currency.name}</h6>
            <span>({currency.code})</span>
          </div>
        </Card>
      </StyledComponent>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(Coin);
