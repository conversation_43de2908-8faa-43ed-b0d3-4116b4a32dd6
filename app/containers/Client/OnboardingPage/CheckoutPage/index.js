import React, {Fragment} from 'react';
import {compose} from 'redux';
import Row from 'reactstrap/es/Row';
import Col from 'reactstrap/es/Col';
import {injectIntl} from 'react-intl';
import Container from 'reactstrap/es/Container';
import Card from 'components/Card';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import DropdownList from 'components/DropdownList';
import {TO, convertDropdownList} from 'utils/utilHelper';
import get from 'lodash/get';
import StyledComponent from './styles';
import messages from "../messages";
import FormInputGroup from 'components/common/FormInputGroup';
import getSchema from './validateSchema';
import Button from 'components/common/Button';
import {Elevation} from "@blueprintjs/core";
import {errorCode} from 'constants/responseCode';
import {placeOrderV2Proxy, redeemCode} from 'services/user.service';
import CryptoMethod from './CryptoMethod';
import StripMethod from './StripeMethod';
import Coin from "./Coin";
import FormMoneyInputGroup from 'components/common/FormMoneyInputGroup';
import {formatCurrency, formatCryptoCurrency} from 'utils/numberHelper';
import {getMinAmount, getEstimatedAmount, createTransfer} from 'services/payment.service';
import ButtonLink from "../../../../components/ButtonLink";

const PAYMENT_NOWPAYMENT = 'NOW_PAYMENT';
const PAYMENT_STRIPE = 'STRIPE';

class CheckoutPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      salePackageSelected: {},
      promotionCode: '',
      isSubmitting: false,
      amount: 0,
      minAmount: 0,
      estimatedAmount: 0,
      currency: '',
      paymentMethod: '',
    };
  }

  componentWillMount() {
    const {
      intl,
      salePackageSelected,
      newOrderInfo,
      handleUpdateOrder
    } = this.props;

    let totalAmount = (salePackageSelected.price * newOrderInfo.quantity);
    if (newOrderInfo.vpn && salePackageSelected.vpnFee) {
      totalAmount = totalAmount + salePackageSelected.vpnFee;
    }
    handleUpdateOrder('totalAmount', totalAmount);

    this.setState({
      salePackageSelected: salePackageSelected,
    })
  }

  handleRedeemCode = async (promotionCode) => {
    console.log('-> Promotion Code', promotionCode);
    if (promotionCode !== null && promotionCode === '') {
      return;
    }

    const {salePackageSelected} = this.state;
    const {handleUpdateOrder, newOrderInfo} = this.props;
    let {totalAmount} = newOrderInfo;
    let discountAmount = 0;

    const [err, response] = await TO(redeemCode(promotionCode, totalAmount));
    this.setState({
      redeemResult: response.code,
    });

    if (response.code === errorCode.SUCCESS) {
      discountAmount = response.data;

      handleUpdateOrder('promotionCode', promotionCode);
      handleUpdateOrder('totalAmount', totalAmount - discountAmount);
      this.setState({
        redeemMessage: `Discount Amount: ${response.data}USD`,
      });
    } else {
      discountAmount = 0;
      totalAmount = (salePackageSelected.price * newOrderInfo.quantity);

      handleUpdateOrder('promotionCode', '');
      handleUpdateOrder('totalAmount', totalAmount - discountAmount);
      this.setState({
        promotionCode: '',
        redeemMessage: response.message
      })
    }
  }

  handleEstimatedAmount = async (amount, currency) => {
    const data = await getEstimatedAmount(currency, amount);
    const estimatedAmount = get(data, 'data.estimated_amount');
    this.setState({
      estimatedAmount,
    })
  }

  handleLoadMinAmount = async (currency) => {
    const {intl, newOrderInfo} = this.props;
    let {totalAmount} = newOrderInfo;

    this.handleEstimatedAmount(totalAmount, currency);

    this.setState({
      currency: currency
    })

    const response = await getMinAmount(currency);
    const errorCode = get(response, 'code');
    if (errorCode === 1) {
      const minAmount = get(response, 'data.min_amount');
      this.setState({
        minAmount: minAmount
      })
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgGetMinFailed));
      this.setState({
        minAmount: 0,
        estimatedAmount: 0
      })
    }
  }

  placeOrder = async () => {
    const {currency} = this.state;
    const {
      intl,
      currencies,
      newOrderInfo,
      handleOrderStep,
      salePackageSelected,
      promotionCode,
      handleCryptoPayment,
      handleStripePayment,
    } = this.props;

    const dataSubmit = {
      packageUuid: salePackageSelected.uuid,
      quantity: newOrderInfo.quantity,
      time: 1,
      username: '',
      whiteListIps: '',
      promotionCode: promotionCode,
      vpnType: newOrderInfo.vpn,
      paymentMethod: newOrderInfo.paymentMethod,
      payCurrency: currency,
      priceCurrency: 'usd',
    };

    this.setState({
      isSubmitting: true,
    })

    console.log('====>ORDER');
    console.log(dataSubmit);

    const [err, response] = await TO(placeOrderV2Proxy(dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.createdError));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.createdSuccess));
      let {paymentUrl, topupDto} = response.data;

      if (newOrderInfo.paymentMethod === PAYMENT_STRIPE) {
        // Handle Stripe
        handleStripePayment(paymentUrl);
      } else if (newOrderInfo.paymentMethod === PAYMENT_NOWPAYMENT) {
        // Handle now_payment
        handleCryptoPayment(true, topupDto);
      }
    } else if (response.message) {
      this.props.handleAlertError(response.message);
    } else {
      this.props.handleAlertError(response.message);
    }

    this.setState({
      isSubmitting: false,
    }, () => {
      handleOrderStep();
    })
  }

  render() {
    const {intl, currencies, newOrderInfo, handleOrderStep, handleUpdateOrder, userBalance} = this.props;
    const {redeemResult, redeemMessage, promotionCode} = this.state;

    return (
      <StyledComponent>

        <Container>
          <div className="mt-1 container-fluid">
            <Card interactive className="card-info p-3">

              <div className="card-header">
                <h4 className="card-title">
                  <i className="fas fa-first-order"></i>
                  Checkout
                </h4>
              </div>

              <Row className="item-callout">
                <Col md={{size: 12}}>
                  <h5>#Order Summary</h5>
                  <div className="callout callout-warning">
                    <Row>
                      <Col md={{size: 6}} className="d-flex flex-row">
                        <h6><strong>Location: &nbsp;</strong></h6>
                        <h6> {get(newOrderInfo, 'location')}</h6>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={{size: 6}} className="d-flex flex-row">
                        <h6><strong>ISP Network: &nbsp;</strong></h6>
                        <h6> {get(newOrderInfo, 'isp')}</h6>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={{size: 6}} className="d-flex flex-row">
                        <h6><strong>VPN Addon: &nbsp;</strong></h6>
                        <h6> {get(newOrderInfo, 'vpn', 'None') === null ? 'None' : get(newOrderInfo, 'vpn', 'None')}</h6>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={{size: 6}} className="d-flex flex-row">
                        <h6><strong>Plan: &nbsp;</strong></h6>
                        <h6>{get(newOrderInfo, 'unit')}</h6>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={{size: 6}} className="d-flex flex-row">
                        <h6><strong>Number of proxies: &nbsp;</strong></h6>
                        <h6> {get(newOrderInfo, 'quantity')}</h6>
                      </Col>
                    </Row>
                  </div>
                </Col>
              </Row>

              <Row className="item-callout">
                <Col md={{size: 12}}>
                  <h5>#Order Balance</h5>
                  <Row>
                    <Col md={{size: 12}}>
                      <h5>Promotion Code</h5>
                      <div>
                        <FormInputGroup
                          name="promotionCode"
                          onChange={(e) => {
                            this.setState({
                              promotionCode: e.target.value,
                            })
                            this.handleRedeemCode(e.target.value);
                          }}
                          type={'text'}
                          value={promotionCode}
                          isMessageSuccess={redeemResult === errorCode.SUCCESS ? redeemMessage : null}
                          isMessageFailed={redeemResult === errorCode.FAILED ? redeemMessage : null}
                        />
                      </div>
                    </Col>
                  </Row>
                  <Row>
                    <Col md={{size: 12}}>
                      <h5>Total amount (USD)</h5>
                      <div>
                        <FormInputGroup
                          disabled
                          name="number"
                          type={'number'}
                          value={get(newOrderInfo, 'totalAmount')}
                        />
                      </div>
                    </Col>
                  </Row>
                </Col>
              </Row>

              {
                (userBalance < get(newOrderInfo, 'totalAmount')) && <Row className="item-callout">
                  <Col md={{size: 12}}>
                    <h5>Add funds</h5>
                    <h7>We do not store your payment information. All transaction are processed through trusted platforms
                      Stripe and Nowpayments.
                    </h7>
                    <div className="mt-2">
                      <h6><strong>Select Payment Methods</strong></h6>
                      <Row>
                        <Col md={{size: 4}} className="mb-3 pt-2 pb-2">
                          <CryptoMethod
                            paymentMethod={get(newOrderInfo, 'paymentMethod')}
                            onClick={() => {
                              handleUpdateOrder('paymentMethod', PAYMENT_NOWPAYMENT);
                            }}
                            method={PAYMENT_NOWPAYMENT}
                            currencies={currencies}
                          />
                        </Col>
                        <Col md={{size: 4}} className="mb-3 p-2">
                          <StripMethod
                            paymentMethod={get(newOrderInfo, 'paymentMethod')}
                            onClick={() => {
                              handleUpdateOrder('paymentMethod', PAYMENT_STRIPE);
                            }}
                            method={PAYMENT_STRIPE}
                          />
                        </Col>
                      </Row>
                      {get(newOrderInfo, 'paymentMethod') === PAYMENT_NOWPAYMENT && <Fragment>
                        <Row>
                          <Col md={{size: 12}}>
                            <div className="d-flex flex-row">
                              {
                                currencies.map((currency) =>
                                  <Coin
                                    currency={currency}
                                    coin={this.state.currency}
                                    onClick={() => {
                                      this.handleLoadMinAmount(currency.code);
                                    }}
                                  />
                                )
                              }
                            </div>
                          </Col>
                        </Row>
                        <Row className="pt-5">
                          <Col md={{size: 12}}>
                            <div className="d-flex flex-column">
                              <FormInputGroup
                                disabled
                                label={`${intl.formatMessage(messages.min_amount)} (Coin)`}
                                name="minAmount"
                                type={'text'}
                                value={`${formatCryptoCurrency(this.state.minAmount)}`}
                                placeHolder={''}
                              />
                              <FormInputGroup
                                disabled
                                label={`${intl.formatMessage(messages.est_amount)} (Coin)`}
                                name="estimatedAmount"
                                type={'text'}
                                value={`${formatCryptoCurrency(this.state.estimatedAmount)}`}
                                placeHolder={''}
                              />
                            </div>
                          </Col>
                        </Row>
                      </Fragment>}
                      {get(newOrderInfo, 'paymentMethod1') === PAYMENT_STRIPE && <Fragment>
                        <Row className="pt-5">
                          <Col md={{size: 12}}>
                            <div className="d-flex flex-column">
                              <FormMoneyInputGroup
                                label={`${intl.formatMessage(messages.input_amount)} (USD)`}
                                name="amount"
                                decimalScale={2}
                                onChange={(e) => {
                                  this.setState({
                                    amount: e.target.value,
                                  })
                                }}
                                value={this.state.amount}
                              />
                            </div>
                          </Col>
                        </Row>
                      </Fragment>}
                    </div>
                  </Col>
                </Row>
              }

              <div className="d-flex flex-column align-items-center">
                <Button
                  primary
                  type="submit"
                  className="min-width-300 mt-2"
                  loading={this.state.isSubmitting}
                  onClick={() => {
                    this.placeOrder();
                  }}
                >{'Payment'}</Button>
                <ButtonLink
                  onClick={handleOrderStep}
                  type={'button'}
                >{`Back`}</ButtonLink>
              </div>

            </Card>
          </div>
        </Container>
      </StyledComponent>
    );
  }
}


export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(CheckoutPage);
