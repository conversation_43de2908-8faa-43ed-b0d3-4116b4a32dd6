import React, {Fragment} from 'react';
import {compose} from 'redux';
import {injectIntl} from 'react-intl';
import styled from 'styled-components';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import {Card, Elevation} from '@blueprintjs/core';
import messages from '../../messages';
import CheckIcon from 'images/check-ico.png';
import PaymentIcon from 'images/payment/powered-by-stripe.png';

const StyledComponent = styled.div`
  h7 {
    padding: 1px 1px 0px 1px !important;
  }
  hr {
    border: none;
    border-top: 1px dotted #f00;
    color: #fff;
    background-color: #fff;
    height: 1px;
    width: 50%;
  }

  .check-box {
    position: absolute;
    top: 10px;
    right: 10px;

    &.show {
      display: inline;
    }

    &.hide {
      display: none;
    }
  }

`;


export class StripeMethod extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      userInfo: {},
    };
  }

  render() {
    const {
      onClick,
      intl,
      paymentMethod,
      value,
      method,
    } = this.props;

    return (
      <StyledComponent>
        <Card
          interactive
          elevation={Elevation.TWO}
          onClick={onClick}
        >
          <div className="mb-1">
            <img src={PaymentIcon} height="100" width="150"/>
            <div className={paymentMethod === method ? 'check-box show' : 'check-box hide'}>
              <img src={CheckIcon} height="50" width="50"/>
            </div>
          </div>
          <div className="d-flex flex-column">
          </div>
        </Card>
      </StyledComponent>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(StripeMethod);
