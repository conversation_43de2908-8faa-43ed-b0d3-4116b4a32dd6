import React, {Fragment} from 'react';
import StyledContainer from 'containers/Client/OnboardingPage/styles';
import {forwardTo} from '../../../utils/history';
import {routes} from 'containers/Routes/routeHelper';
import {injectIntl} from 'react-intl';
import {compose} from 'redux';
import messages from './messages';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import {Card, Tab} from '@blueprintjs/core';
import {Row, Col} from 'reactstrap';
import {getPackages} from 'services/admin/package.service';
import {getUserBalance, getAllLocations} from 'services/user.service';
import {formatCurrency} from 'utils/numberHelper';
import OrderPage from './OrderPage';
import CheckoutPage from './CheckoutPage';
import Container from 'reactstrap/es/Container';
import {getCurrencies} from 'services/payment.service';
import GuidePopup from "../RechargePage/GuidePopup";

export class OnboardingPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      dataList: [],
      salePackages: [],
      locations: [],
      salePackage: null,
      isOpenPopup: false,
      forceRefresh: false,
      config: {
        balance: 0,
        totalLicense: 0,
        totalExpiredLicense: 0,
      },
      isOrderStep: true,
      isCheckoutStep: false,
      newOrderInfo: {},
      currencies: [],
      saleLocations: [],
      saleUnits: [],
    };
  }

  componentWillReceiveProps(nextProps) {
  }

  componentWillMount() {
    this.handleGetUserInfo();
    this.handleGetPackages();
    this.handleGetLocations();
    this.loadInitData();
  }

  loadInitData = async () => {
    const resp = await getCurrencies();
    this.setState({
      currencies: resp && resp.code === 1 ? resp.data : [],
    });
  }

  handleGetLocations = () => {
    this.props.handlePromise(getAllLocations(), (response) => {
      const {data} = response;
      this.setState({
        locations: data,
      });
    });
  }

  handleGetPackages = () => {
    const requestBody = {
      filtered: [{
        id: 'status',
        value: 'ACTIVE',
      }],
      pageSize: 20,
      page: 0,
    };
    this.props.handlePromise(getPackages(requestBody), (response) => {
      const {data} = response.data;

      // Location
      let locations = [];
      const listLocationSelected = [...new Set(data.map(item => item.location))];
      for (let i = 0; i < listLocationSelected.length; i++) {
        locations.push({
          id: i + 100,
          name: listLocationSelected[i],
        });
      }

      // Unit
      let units = [];
      const listUnitSelected = [...new Set(data.map(item => item.packageUnit))];
      for (let i = 0; i < listUnitSelected.length; i++) {
        units.push({
          id: listUnitSelected[i],
          name: listUnitSelected[i],
        });
      }

      this.setState({
        salePackages: data,
        saleLocations: locations,
        saleUnits: units,
      });
    });
  }

  handleGetUserInfo = () => {
    this.props.handlePromise(getUserBalance(), (response) => {
      this.setState({
        config: {
          ...response,
        },
      });
    });
  }

  handleCheckoutStep = () => {
    this.setState({
      isCheckoutStep: true,
      isOrderStep: false
    })
  }

  handleOrderStep = () => {
    this.setState({
      isCheckoutStep: false,
      isOrderStep: true
    })
  }

  handleUpdateOrder = (key, value, callback = null) => {
    console.log('Key: ' + key + '; Value:' + value);
    let {newOrderInfo} = this.state;
    this.setState({
      newOrderInfo: {
        ...newOrderInfo,
        [key]: value
      }
    });

    if (callback)
      callback();
  }

  handleUpdateSalePackage = (salePackage) => {
    this.setState({
      salePackageSelected: salePackage
    });
  }

  handleCryptoPayment = (isOpen = true, cryptoPaymentObject) => {
    this.handleOrderStep();
    this.setState({
      isOpenGuide: isOpen,
      cryptoPaymentObject,
    });
  }

  handleStripePayment = (paymentUrl) => {
    this.handleOrderStep();
    window.open(paymentUrl, '_blank');
  }

  render() {
    const {intl} = this.props;
    const {
      salePackages,
      salePackageSelected,
      config,
      newOrderInfo,
      isCheckoutStep,
      isOrderStep,
      currencies,
      isOpenGuide,
      cryptoPaymentObject,
      saleLocations,
      saleUnits,
    } = this.state;

    return (
      <StyledContainer>
        <Fragment>
          <Container>
            <Row className="mt-3 mb-3">
              <Col md={{size: 3}}>
                <Card interactive className="card-info"
                      onClick={() => forwardTo(routes.CLIENT_RECHARGE)}>
                  <blockquote>
                    <h6>{intl.formatMessage(messages.balance)}: <b>{formatCurrency(config.balance)} USD</b>
                    </h6>
                  </blockquote>
                </Card>
              </Col>
              <Col md={{size: 3}}>
                <Card interactive className="card-info" onClick={() => forwardTo(routes.CLIENT_PROXY)}>
                  <blockquote>
                    <h6>{intl.formatMessage(messages.total_license)}: <b>{config.totalLicense}</b>
                    </h6>
                  </blockquote>
                </Card>
              </Col>
              <Col md={{size: 4}}>
                <Card interactive className="card-info" onClick={() => forwardTo(routes.CLIENT_PROXY)}>
                  <blockquote>
                    <h6>{intl.formatMessage(messages.total_license_expired)}: <b>{config.totalExpiredLicense}</b>
                    </h6>
                  </blockquote>
                </Card>
              </Col>

            </Row>
          </Container>

          {isOrderStep && <OrderPage
            newOrderInfo={newOrderInfo}
            salePackageSelected={salePackageSelected}
            handleOrderStep={() => this.handleOrderStep()}
            handleCheckoutStep={() => this.handleCheckoutStep()}
            handleUpdateOrder={this.handleUpdateOrder}
            handleUpdateSalePackage={this.handleUpdateSalePackage}
            salePackages={salePackages}
            saleLocations={saleLocations}
            saleUnits={saleUnits}
            className="pt-5"
          ></OrderPage>}
          {isCheckoutStep && <CheckoutPage
            newOrderInfo={newOrderInfo}
            handleCheckoutStep={() => this.handleCheckoutStep()}
            handleOrderStep={() => this.handleOrderStep()}
            handleUpdateOrder={this.handleUpdateOrder}
            salePackageSelected={salePackageSelected}
            className="pt-5"
            currencies={currencies}
            handleCryptoPayment={this.handleCryptoPayment}
            handleStripePayment={this.handleStripePayment}
            userBalance={config.balance}
          ></CheckoutPage>}

          <GuidePopup
            isOpen={isOpenGuide}
            handleOnClose={() => this.handleCryptoPayment(false)}
            transaction={cryptoPaymentObject}
          />

        </Fragment>
      </StyledContainer>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(OnboardingPage);
