import React from 'react';
import { injectIntl } from 'react-intl';
import Button from 'components/common/Button';
import ButtonLink from 'components/ButtonLink';
import ActionDialog from 'components/common/ActionDialog';
import { TO, convertDropdownList } from 'utils/utilHelper';
import messages from '../messages';
import styled from 'styled-components';
import { Formik, Form } from 'formik';
import { Row, Col } from 'reactstrap';
import get from 'lodash/get';
import DropdownList from 'components/common/DropdownList';
import { compose } from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import { errorCode } from 'constants/responseCode';
import * as Yup from 'yup';
import { extendLicense } from 'services/user.service';
import getSchema from './validateSchema';

const StyledContainer = styled(ActionDialog)`

`;

class ExtendPopup extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initData: {
        packageId: '',
      },
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.selectedIds !== undefined) {
      this.setState({
        initData: {
          packageId: '',
        },
      });
    }
  }

  handleSubmit = async (values, { setSubmitting }) => {
    const { forceRefresh, handleOnClose, intl, selectedIds } = this.props;

    const dataSubmit = {
      packageId: values.packageId,
      uuids: selectedIds,
    };

    const [err, response] = await TO(extendLicense(dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.msgUpdateSuccess));
      handleOnClose();
      forceRefresh();
    } else if (response.message) {
      this.props.handleAlertError(response.message);
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    setSubmitting(false);
  }


  render() {
    const self = this;
    const {
      intl,
      isOpen,
      handleOnClose,
      packageUnit,
      packages,
      location,
    } = this.props;

    const {
      initData,
    } = self.state;

    const upgrade = {
      MINUTES: [
        'MINUTES',
        'DAY',
        'WEEK',
        'MONTH',
      ],
      DAY: [
        'DAY',
        'WEEK',
        'MONTH',
      ],
      WEEK: [
        'WEEK',
        'MONTH',
      ],
      MONTH: [
        'MONTH',
      ],
    };

    let packagesOptions = [];
    const packageUpgrades = upgrade[packageUnit];
    if (packageUpgrades) {
      const filterPackages = packages.filter((i) => packageUpgrades.includes(i.packageUnit) && i.location.toLowerCase() === location.toLowerCase());
      packagesOptions = convertDropdownList(filterPackages.map((i) => (
        {
          name: `${i.name}`,
          id: i.uuid,
        }
      )));
    }

    return (
      <StyledContainer
        portalClassName="custom-portal"
        title={'Extend License'}
        usePortal
        canOutsideClickClose
        canEscapeKeyClose
        isOpen={isOpen}
        onClose={handleOnClose}
        width={450}
      >
        <Wrapper className="m-4">
          <Formik
            onSubmit={this.handleSubmit}
            initialValues={initData}
            enableReinitialize
            validationSchema={getSchema(intl)}
            render={(props) => (
              <Form>
                <Row>
                  <Col md={{ size: 12 }}>
                    <DropdownList
                      label={'Packages'}
                      isAsterisk
                      name="packageId"
                      value={packagesOptions.find((option) =>
                        option.value === get(props.values, 'packageId')
                      )}
                      options={packagesOptions}
                      onChange={(option) => {
                        props.setFieldValue('packageId', option.value);
                      }}
                      {...props}
                    />
                  </Col>
                </Row>
                <div className="d-flex flex-column align-items-center">
                  <div className="d-flex">
                    <Button
                      primary
                      type="submit"
                      className="min-width-100 mt-4 mr-1"
                      loading={props.isSubmitting}
                    >{'Extend'}</Button>
                  </div>
                  <ButtonLink
                    onClick={handleOnClose}
                    type={'button'}
                  >{intl.formatMessage(messages.close)}</ButtonLink>
                </div>
              </Form>
            )}
          />
        </Wrapper>
      </StyledContainer>
    );
  }
}


ExtendPopup.propTypes = {};

const Wrapper = styled.div`
  margin-bottom: 10px;

  .content {
    border: 1px solid ${(props) => props.theme.colors.gray300};
    display: flex;
    flex-wrap: wrap;

    .label {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.small12};
      opacity: 0.5;
    }

    .bold-text {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.normal};
      font-weight: ${(props) => props.theme.fontWeights.strong};
      opacity: 0.8;
    }

    .group {
      padding: 10px 18px;
      background-color: ${(props) => props.theme.colors.white};
      width: 50%;

      &.gray {
        background-color: ${(props) => props.theme.colors.gray};
      }
    }
  }
`;


export default compose(
  WithHandleAlert,
  injectIntl
)(ExtendPopup);
