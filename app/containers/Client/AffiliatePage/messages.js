/*
 * AccountManagementPage Messages
 *
 * This contains all the text for the Test component.
 */
import { defineMessages } from 'react-intl';

const scope = 'app.components.common';

export default defineMessages({
  affiliate_title: {
    id: `${scope}.affiliate_title`, defaultMessage: 'The affiliate program is running and super hot',
  },
  aff_info_1: {
    id: `${scope}.aff_info_1`, defaultMessage: 'You will receive',
  },
  aff_info_1_2: {
    id: `${scope}.aff_info_1_2`, defaultMessage: 'commission from any deposit of the friends you have referred.',
  },
  aff_info_2: {
    id: `${scope}.aff_info_2`,
    defaultMessage: 'commission will be effective immediately upon successful deposit transaction from your friends.',
  },
  aff_info_3: {
    id: `${scope}.aff_info_3`,
    defaultMessage: 'The system will keep cookies for 60 days from the time you click on your Affiliate link',
  },
  aff_info_4: {
    id: `${scope}.aff_info_4`,
    defaultMessage: 'Please share your referral link with your friends who register for an account in the system',
  },
  aff_info_5: {
    id: `${scope}.aff_info_5`, defaultMessage: 'Your referral link',
  },

});
