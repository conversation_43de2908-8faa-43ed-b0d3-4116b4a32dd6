import React, { Fragment } from 'react';
import { compose } from 'redux';
import Row from 'reactstrap/es/Row';
import Col from 'reactstrap/es/Col';
import { injectIntl } from 'react-intl';
import Block from './Block';
import iconCampaign from 'images/formIcon/icoConfirmOTP.svg';
import Container from 'reactstrap/es/Container';
import Card from 'components/Card';
import styled from 'styled-components';
import { getUserBalance } from 'services/user.service';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import ButtonCopy from 'components/common/ButtonCopy';
import env from 'env';
import messages from './messages';

const StyledComponent = styled.div`
  table {
        .title {
            width: 60%;
            font-size: 16px;
            font-weight: bold;
            text-align: right;
        }
        .content {
            font-weight: 400;
            padding-top: 5px;
            padding-left: 10px;
            font-size: 16px;
         }
   }
     
  .row {
    max-width: 860px;
    margin: 0 auto;
  }
`;


export class AccountManagementPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      userInfo: {
        name: '',
        userName: '',
        email: '',
        balance: 0,
        uuid: '',
        affiliateCode: '',
        affiliateCommissionRate: '',
      },
    };
  }

  componentWillMount() {
    this.props.handlePromise(getUserBalance(), (response) => {
      this.setState({
        userInfo: {
          ...response,
        },
      });
    });
  }


  render() {
    const { intl } = this.props;
    const { userInfo } = this.state;

    return (
      <StyledComponent>
        <Container>
          <Card>
            <Row>
              <Col md={12}>
                <Block
                  title={intl.formatMessage(messages.affiliate_title)}
                  icon={iconCampaign}
                >
                  <p> - {intl.formatMessage(messages.aff_info_1)} {userInfo.affiliateCommissionRate}% {intl.formatMessage(messages.aff_info_1_2)}</p>
                  <p> - {userInfo.affiliateCommissionRate}% {intl.formatMessage(messages.aff_info_2)}</p>
                  <p> - {intl.formatMessage(messages.aff_info_3)}</p>
                  <p> - {intl.formatMessage(messages.aff_info_4)}</p>
                  <p> <div>- {intl.formatMessage(messages.aff_info_5)}:</div>
                    <div style={{ width: 190 }}>
                      <ButtonCopy content={userInfo.affiliateCode} copyValue={`${env.APP_URL}/register?code=${userInfo.affiliateCode}`} btnText={'Copy'} className="font-weight-bolder" style={{ color: 'gray' }} />
                    </div>
                  </p>
                </Block>
              </Col>
            </Row>
          </Card>
        </Container>
      </StyledComponent>
    );
  }
}


export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(AccountManagementPage);
