/*
 * ChangePasswordPage Messages
 *
 * This contains all the text for the Test component.
 */
import { defineMessages } from 'react-intl';

const scope = 'rb.containers.ChangePasswordPage';

export default defineMessages({
  title: {
    id: `${scope}.title`,
    defaultMessage: 'Reset password',
  },

  subTitle: {
    id: `${scope}.subTitle`,
    defaultMessage: 'To reset your password, please enter your phone number below',
  },

  currentPassword: {
    id: `${scope}.currentPassword`,
    defaultMessage: 'Current password',
  },

  newPassword: {
    id: `${scope}.newPassword`,
    defaultMessage: 'Enter new password',
  },

  confirmPassword: {
    id: `${scope}.confirmPassword`,
    defaultMessage: 'Confirm password',
  },

  update: {
    id: `${scope}.update`,
    defaultMessage: 'Update',
  },

  messageSuccess: {
    id: `${scope}.messageSuccess`,
    defaultMessage: 'Your password has been changed successfully.',
  },

  messageInvalidNewPassword: {
    id: `${scope}.messageInvalidNewPassword`,
    defaultMessage: 'The new password must not be the same as the old password.',
  },
  currentPasswordRequiredError: {
    id: `${scope}.currentPasswordRequiredError`,
    defaultMessage: 'Please enter your current password.',
  },
  new_passwordRequiredError: {
    id: `${scope}.new_passwordRequiredError`,
    defaultMessage: 'Please enter a new password.',
  },
  new_passwordMaxLengthError: {
    id: `${scope}.new_passwordMaxLengthError`,
    defaultMessage: 'Please enter a new password that is less than 30 characters.',
  },
  new_passwordMinLengthError: {
    id: `${scope}.new_passwordMinLengthError`,
    defaultMessage: 'Please enter more than 8 characters.',
  },
  new_passwordStrongError: {
    id: `${scope}.new_passwordStrongError`,
    defaultMessage: 'Password must be between 8 and 15 characters, with at least 1 letter and number.',
  },
  confirm_passwordRequiredError: {
    id: `${scope}.confirm_passwordRequiredError`,
    defaultMessage: 'Please re-enter the password.',
  },
  passwordNotMatchError: {
    id: `${scope}.passwordNotMatchError`,
    defaultMessage: 'Authentication password does not match.',
  },
});
