import { defineMessages } from 'react-intl';

const scope = 'app.components.common';

export default defineMessages({
  requiredNotNull: {
    id: 'app.components.common.requiredNotNull',
    defaultMessage: 'This field is required',
  },
  requiredMinAmount: {
    id: 'app.components.common.requiredMinAmount',
    defaultMessage: 'Please enter more than 10$',
  },
  close: {
    id: 'app.components.common.close',
    defaultMessage: 'Close',
  },
  msgCreateSuccess: {
    id: 'app.components.common.msgCreateSuccess',
    defaultMessage: 'Create new data successfully',
  },
  msgCreateFailed: {
    id: 'app.components.common.msgCreateFailed',
    defaultMessage: 'Create new data failed',
  },
  msgGetMinFailed: {
    id: 'app.components.common.msgGetMinFailed',
    defaultMessage: 'Can not get minimum amount of this curreny. Please choose another currency',
  },
  msgEstAmountFailed: {
    id: 'app.components.common.msgEstAmountFailed',
    defaultMessage: 'Please top-up Amount (USD) satisfy that: Estimate Amount (Coin) is greater than Min Amount (Coin).',
  },
  thank_for_recharge: {
    id: `${scope}.thank_for_recharge`,
    defaultMessage: 'Thanks for your recharge. Please check at Transaction History!',
  },
  system_interrupted: {
    id: `${scope}.system_interrupted`,
    defaultMessage: 'System is interrupted. Please try again or contact support!',
  },
  error_top_up: {
    id: `${scope}.error_top_up`,
    defaultMessage: 'Error forward top-up transaction. Please try again',
  },
  recharge_success: {
    id: `${scope}.recharge_success`,
    defaultMessage: 'Recharge successful. New balance',
  },
  top_up_with: {
    id: `${scope}.top_up_with`,
    defaultMessage: 'TOPUP YOUR ACCOUNT WITH',
  },
  crypto_now: {
    id: `${scope}.crypto_now`,
    defaultMessage: 'CRYPTO NOW',
  },
  purchase_a_proxy: {
    id: `${scope}.purchase_a_proxy`,
    defaultMessage: 'TO PURCHASE A PROXY',
  },
  crypto_1: {
    id: `${scope}.crypto_1`,
    defaultMessage: '1. Click the \'Add Funds\' button to begin',
  },
  crypto_2: {
    id: `${scope}.crypto_2`,
    defaultMessage: '2. Choose the currency between BTC or USDT (Trc20)',
  },
  crypto_3: {
    id: `${scope}.crypto_3`,
    defaultMessage: '3. Input the amount you want to deposit, and create the order.',
  },
  crypto_4: {
    id: `${scope}.crypto_4`,
    defaultMessage: '4.  Once payment has been completed, the funds will be available to purchase.',
  },
  card_now: {
    id: `${scope}.card_now`,
    defaultMessage: 'YOUR CARD',
  },
  card_1: {
    id: `${scope}.card_1`,
    defaultMessage: '1. Click the \'Add Funds\' button to begin.',
  },
  card_2: {
    id: `${scope}.card_2`,
    defaultMessage: '2. Input the amount you want to deposit, and create the checkout.',
  },
  card_3: {
    id: `${scope}.card_3`,
    defaultMessage: '3. You’re redirected to the Stripe OrderPage payment form.',
  },
  card_4: {
    id: `${scope}.card_4`,
    defaultMessage: '4. Fill out the payment details with the card information.',
  },
  card_5: {
    id: `${scope}.card_5`,
    defaultMessage: '5. Click Pay.',
  },
  card_6: {
    id: `${scope}.card_6`,
    defaultMessage: '6. You’re redirected to your new success page. The funds will be available to purchase.',
  },
  topup_add: {
    id: `${scope}.topup_add`,
    defaultMessage: 'Please top-up add',
  },
  deposit_money: {
    id: `${scope}.deposit_money`, defaultMessage: 'Deposit Money',
  },
  input_amount: {
    id: `${scope}.input_amount`, defaultMessage: 'Input Amount',
  },
  copy_success: {
    id: `${scope}.copy_success`, defaultMessage: 'Copy to clipboard successful!',
  },
  trans_info: {
    id: `${scope}.trans_info`, defaultMessage: 'Transfer information',
  },
  price: {
    id: `${scope}.price`, defaultMessage: 'Price',
  },
  amount: {
    id: `${scope}.amount`, defaultMessage: 'Amount',
  },
  address: {
    id: `${scope}.address`, defaultMessage: 'Address',
  },
  tip_coin_1: {
    id: `${scope}.tip_coin_1`, defaultMessage: 'Please sends coins with the transfer information.',
  },
  tip_coin_2: {
    id: `${scope}.tip_coin_2`,
    defaultMessage: 'After sends coins, system will processes and exchanges them, and settles the payment to your balance.',
  },
  min_amount: {
    id: `${scope}.min_amount`, defaultMessage: 'Min Amount',
  },
  est_amount: {
    id: `${scope}.est_amount`, defaultMessage: 'Estimate Amount',
  },
  created_date: {
    id: `${scope}.created_date`, defaultMessage: 'Created date',
  },
  trans_type: {
    id: `${scope}.trans_type`, defaultMessage: 'Transaction type',
  },
  status: {
    id: `${scope}.status`, defaultMessage: 'Status',
  },
  description: {
    id: `${scope}.description`, defaultMessage: 'Description',
  },
  action: {
    id: `${scope}.action`, defaultMessage: 'Action',
  },
  guide: {
    id: `${scope}.guide`, defaultMessage: 'Guide',
  },
  trans_his: {
    id: `${scope}.trans_his`, defaultMessage: 'Transaction History',
  },

});
