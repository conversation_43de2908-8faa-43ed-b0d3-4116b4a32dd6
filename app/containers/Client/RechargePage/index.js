import React, { Fragment } from 'react';
import StyledContainer from 'containers/Client/RechargePage/styles';
import { forwardTo } from '../../../utils/history';
import { routes } from 'containers/Routes/routeHelper';
import { injectIntl } from 'react-intl';
import { compose } from 'redux';
import messages from './messages';
import { TO, convertDropdownList } from 'utils/utilHelper';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import auth from 'utils/auth';
import { Elevation } from '@blueprintjs/core';
import { Row, Col } from 'reactstrap';
import { getUserBalance } from 'services/user.service';
import { getCurrencies } from 'services/payment.service';
import { formatCurrency } from 'utils/numberHelper';
import TransactionList from './TransactionList';
import Card from 'components/Card';
import ButtonCreate from 'components/common/ButtonCreate';
import TopupPopup from './TopupPopup';
import CheckoutPopup from './CheckoutPopup';
import GuidePopup from './GuidePopup';
import { call } from 'redux-saga/effects';

export class RechargePage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      dataList: [],
      intervalId: 0,
      config: {
        balance: 0,
        uuid: '',
        stripeEnable: 0,
      },
      currencies: [],
    };
  }

  componentWillReceiveProps(nextProps) {
  }

  componentWillMount() {
    this.handleRecharge(true);
    this.loadInitData();
    const { intl } = this.props;

    const query = new URLSearchParams(location.search);
    const payment = query.get('payment');
    const result = query.get('result');
    if (payment !== null && payment === 'stripe') {
      if (result !== null && result === 'success') {
        this.props.handleAlertSuccess(intl.formatMessage(messages.thank_for_recharge));
      } else {
        this.props.handleAlertError(intl.formatMessage(messages.system_interrupted));
      }
    }

    const method = query.get('method');
    if (method !== null) {
      try {
        const purchaseData = query.get('purchaseData');
        const purchaseObj = JSON.parse(atob(purchaseData));
        this.props.handleAlertSuccess(`${intl.formatMessage(messages.topup_add)} ${purchaseObj.totalAmount}$!!!`);
        if (purchaseObj !== undefined && purchaseObj.totalAmount !== undefined) {
          if (method === 'CRYPTO') {
            this.handleTopup(true);
          } else if (method === 'CARD') {
            this.handleCheckout(true);
          }
        } else {
          this.props.handleAlertError(`${intl.formatMessage(messages.error_top_up)} [${purchaseObj.totalAmount}]`);
        }
      } catch (error) {
        console.log(error);
      }
    }
  }

  loadInitData = async () => {
    const resp = await getCurrencies();
    this.setState({
      currencies: resp && resp.code === 1 ? resp.data : [],
    });
  }


  componentDidMount() {
    const intervalId = setInterval(this.handleRecharge.bind(this), 5000);
    this.setState({ intervalId });
  }

  componentWillUnmount() {
    clearInterval(this.state.intervalId);
  }

  forceRefresh = () => {
    this.setState({
      forceRefresh: !this.state.forceRefresh,
    });
  }

  handleRecharge(isFirst) {
    const { intl } = this.props;
    this.props.handlePromise(getUserBalance(), (response) => {
      if (response.balance !== this.state.config.balance && !isFirst) {
        this.props.handleAlertSuccess(`${intl.formatMessage(messages.recharge_success)} $${formatCurrency(response.balance)}`);
        this.forceRefresh();
      }

      this.setState({
        config: {
          ...response,
        },
      });
    });
  }

  handleUpdateList = (data) => {
    this.setState({
      dataList: data,
    });
    if (data) {
      data.forEach((item) => {
        if ((item.status === 'AWAITING_PAYMENT' || item.status === 'PROCESSING' || item.status === 'PARTIALLY_PAID') && item.note !== '') {
          this.handleGuide(true, item.uuid);
        }
      });
    }
  }

  handleTopup = (isOpen = true) => {
    this.setState({
      isOpenTopup: isOpen,
    });
  }

  handleCheckout = (isOpen = true) => {
    this.setState({
      isOpenCheckout: isOpen,
    });
  }

  handleGuide = (isOpen = true, selectedId) => {
    const { dataList } = this.state;
    const selectedObject = dataList.find((item) => item.uuid === selectedId);

    this.setState({
      isOpenGuide: isOpen,
      selectedObject,
    });
  }

  getKeyFromFilteredList = () => {
    const { forceRefresh } = this.state;
    return `${forceRefresh}`;
  }


  render() {
    const { intl } = this.props;
    const { dataList, config, isOpenTopup, isOpenCheckout, isOpenGuide, currencies, selectedObject } = this.state;

    return (
      <StyledContainer>
        <Fragment>
          <Row className="p-12">
            <Col md={{ size: 12 }} className="pl-5 pr-5 pt-1">
              <Card className="align-items-start pb-3">
                <div className="d-flex">
                  <h5><i className="fa fa-dolly-flatbed" />
                    &nbsp; {intl.formatMessage(messages.top_up_with)} <strong>{intl.formatMessage(messages.crypto_now)}</strong> {intl.formatMessage(messages.purchase_a_proxy)}
                  </h5>
                  <div className="ml-3">
                    <ButtonCreate
                      onClick={() => this.handleTopup(true)}
                      animation
                      title={'Topup'}
                    />
                  </div>
                </div>
                <span style={{ textAlign: 'left', fontSize: 16 }}>
                  <i className="fa fa-check" /> {intl.formatMessage(messages.crypto_1)}
                </span>
                <span style={{ textAlign: 'left', fontSize: 16 }}>
                  <i className="fa fa-check" /> {intl.formatMessage(messages.crypto_2)}
                </span>
                <span style={{ textAlign: 'left', fontSize: 16 }}>
                  <i className="fa fa-check" /> {intl.formatMessage(messages.crypto_3)}
                </span>
                <span style={{ textAlign: 'left', fontSize: 16 }}>
                  <i className="fa fa-check" /> {intl.formatMessage(messages.crypto_4)}
                </span>
              </Card>
            </Col>
          </Row>

          {config.stripeEnable === 1 &&
          <Row className="p-12">
            <Col md={{ size: 12 }} className="pl-5 pr-5 pt-1">
              <Card className="align-items-start pb-3">
                <div className="d-flex">
                  <h5><i className="fa fa-dolly-flatbed" />
                    &nbsp; {intl.formatMessage(messages.top_up_with)}<strong>{intl.formatMessage(messages.card_now)}</strong> {intl.formatMessage(messages.purchase_a_proxy)}
                  </h5>
                  <div className="ml-3">
                    <ButtonCreate
                      onClick={() => this.handleCheckout(true)}
                      animation
                    />
                  </div>
                </div>
                <span style={{ textAlign: 'left', fontSize: 16 }}>
                  <i className="fa fa-check" /> {intl.formatMessage(messages.card_1)}
                </span>
                <span style={{ textAlign: 'left', fontSize: 16 }}>
                  <i className="fa fa-check" /> {intl.formatMessage(messages.card_2)}
                </span>
                <span style={{ textAlign: 'left', fontSize: 16 }}>
                  <i className="fa fa-check" /> {intl.formatMessage(messages.card_3)}
                </span>
                <span style={{ textAlign: 'left', fontSize: 16 }}>
                  <i className="fa fa-check" /> {intl.formatMessage(messages.card_4)}
                </span>
                <span style={{ textAlign: 'left', fontSize: 16 }}>
                  <i className="fa fa-check" /> {intl.formatMessage(messages.card_5)}
                </span>
                <span style={{ textAlign: 'left', fontSize: 16 }}>
                  <i className="fa fa-check" /> {intl.formatMessage(messages.card_6)}
                </span>
              </Card>
            </Col>
          </Row>
          }

          <Row className="p-12 mt-2">
            <Col md={{ size: 12 }} className="pl-5 pr-5 pt-1">
              <div>
                <Row className="mt-3">
                  <Col md={{ size: 12 }}>
                    <h6>{intl.formatMessage(messages.trans_his)}</h6>
                    <Card>
                      <div className="mb-2 d-flex justify-content-end" style={{ width: '100%' }}>
                      </div>
                      <TransactionList
                        dataList={dataList}
                        getKeyFromFilteredList={this.getKeyFromFilteredList}
                        handleUpdateList={this.handleUpdateList}
                        handleGuide={this.handleGuide}
                      />
                    </Card>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>

        </Fragment>
        <TopupPopup
          isOpen={isOpenTopup}
          handleOnClose={() => this.handleTopup(false)}
          forceRefresh={this.forceRefresh}
          currencies={currencies}
        />
        <CheckoutPopup
          isOpen={isOpenCheckout}
          handleOnClose={() => this.handleCheckout(false)}
          forceRefresh={this.forceRefresh}
        />
        <GuidePopup
          isOpen={isOpenGuide}
          handleOnClose={() => this.handleGuide(false)}
          forceRefresh={this.forceRefresh}
          transaction={selectedObject}
        />
      </StyledContainer>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(RechargePage);
