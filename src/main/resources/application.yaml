spring:
  application:
    name: Demo Proxy Business Pro
    url: http://localhost:3000
    verify_email: true
    affiliate_module: true
    listIsp: ["T-Mobile", "Verizon Wireless"]
  servlet:
    path: ${BASE_PATH:}
  jpa:
    properties:
      hibernate:
        jdbc:
          time_zone: UTC
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  flyway:
    enabled: true
    baseline-on-migrate: true
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: ${app.timezone}
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

  mail:
    host: smtp.mailtrap.io
    port: 587
    username: "a913dc1225d300"
    password: "f6481fc5386120"
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

springdoc:
  api-docs:
    enabled: false

server:
  servlet:
    context-path: ${SERVER_SERVLET_CONTEXT_PATH:/neoproxy}
  port: ${APP_PORT:8080}

management:
  security:
    enabled: false
  endpoints:
    web:
      exposure:
        include: health,beans,loggers,env,scheduledtasks,metrics
  endpoint:
    health:
      group:
        liveness:
          include: "livenessState"
        readiness:
          include: "readinessState,diskSpace,db"
      show-details: always
      show-components: always
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true

app:
  timezone: UTC
  jwt:
    secret: ${PROXY_JWT_SECRET:jwt-secret-value}
    token-validity: 3600 # second
    refresh-token-validity: 86400 # second
  scheduler:
    modemSyncCron: 0 0 0 * * ?
    modemSyncIspCron: 0/5 0 0 ? * *
    checkLicenseExpiredCron: 0 0 0 * * ?
    checkTopupStatusCron: 0 0 0 * * ?
    availableProxyAlertCron: 0 0 0 * * ? # 0 0 0/12 * * ?
    autoRotationIpCron: 0 0 0 * * ? # 0 * * * * ?
    alertLicenseExpiredCron: 0 0 0 * * ? # 0 0 0 * * ?
    proxySaleSyncCron: 0 0 0 * * ? # 0 0 0 * * ?
    checkSpeedTestCron: 0 0 0 * * ?
    checkPurchaseStatusCron: 0 0 * * * ?
  thread:
    corePoolSize: 50
    maxPoolSize: 60
    queueCapacity: 100
  xproxy-configuration:
    getListPosition:
      url: api/min/get-list-position
      method: GET
      retryWhenTimeout: true
      retriedTimes: 3
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    getCurrentProxy:
      url: api/min/get-current-proxy
      method: GET
      retryWhenTimeout: true
      retriedTimes: 3
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    getNewProxy:
      url: api/min/get-new-proxy
      method: GET
      retryWhenTimeout: true
      retriedTimes: 3
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    infoList:
      url: /api/v1/info_list
      method: GET
      retryWhenTimeout: true
      retriedTimes: 0
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    sharedProxies:
      url: /selling/shared_proxies?page=1&limit=1000
      method: GET
      retryWhenTimeout: true
      retriedTimes: 2
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    generatePort:
      url: /selling/generate
      method: POST
      retryWhenTimeout: true
      retriedTimes: 0
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    bulkEdit:
      url: /selling/bulk_edit
      method: POST
      retryWhenTimeout: true
      retriedTimes: 0
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    resetDataCounter:
      url: /selling/reset_data_counter
      method: POST
      retryWhenTimeout: true
      retriedTimes: 0
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    resetPort:
      url: /reset
      method: GET
      retryWhenTimeout: true
      retriedTimes: 0
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    rebootPort:
      url: /api/v1/reboot/proxy
      method: GET
      retryWhenTimeout: true
      retriedTimes: 0
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    speedTestProxy:
      url: /v2/speedtest_proxy
      method: POST
      retryWhenTimeout: true
      retriedTimes: 0
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    vpnServerConfig:
      url: /vpn_server_config
      method: POST
      retryWhenTimeout: true
      retriedTimes: 0
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
    vpnDownload:
      url: /vpn_download
      method: GET
      retryWhenTimeout: true
      retriedTimes: 0
      retriedCodes: 500
      requestHeaders:
        Content-Type: "application/json"
  nowpayment-configuration:
    availableCurrencies:
      url: https://api-sandbox.nowpayments.io/v1/currencies
      method: GET
      statusSuccessCodes: [200, 201]
      requestHeaders:
        Content-Type: "application/json"
    selectedCurrencies:
      url: https://api-sandbox.nowpayments.io/v1/merchant/coins
      method: GET
      statusSuccessCodes: [ 200, 201 ]
      requestHeaders:
        Content-Type: "application/json"
    fullCurrencies:
      url: https://api-sandbox.nowpayments.io/v1/full-currencies
      method: GET
      statusSuccessCodes: [ 200, 201 ]
      requestHeaders:
        Content-Type: "application/json"
    minimumAmount:
      url: https://api-sandbox.nowpayments.io/v1/min-amount
      method: GET
      statusSuccessCodes: [200, 201]
      requestHeaders:
        Content-Type: "application/json"
    estimatedPrice:
      url: https://api-sandbox.nowpayments.io/v1/estimate
      method: GET
      statusSuccessCodes: [200, 201]
      requestHeaders:
        Content-Type: "application/json"
    payment:
      url: https://api-sandbox.nowpayments.io/v1/payment
      method: POST
      statusSuccessCodes: [200, 201]
      requestHeaders:
        Content-Type: "application/json"
    paymentStatus:
      url: https://api-sandbox.nowpayments.io/v1/payment
      method: GET
      statusSuccessCodes: [200, 201]
      requestHeaders:
        Content-Type: "application/json"

logging.level:
  com.neoproxy.pro: INFO
  org.springframework: INFO
  org.hibernate: INFO

--- # Dev
spring:
  config:
    activate:
      on-profile: dev
  datasource:
#    url: **************************************************************************************
#    username: proxydb
#    password: evWsnqDu&2AfD(6j
    url: ********************************************
    username: admin
    password: admin
    hikari:
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000
      maximumPoolSize: 10
  jpa:
    show-sql: false
    properties:
      hibernate.format_sql: false